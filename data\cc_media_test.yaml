config:
    name: "渠道管理"
teststeps:
    - name: ""
      request:
        method: POST
        url: /cc-media/webcall
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "9168"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-media/pages/media/channel-edit.jsp?entId=
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
        cookies:
            HMACCOUNT: 028EBC18F491ADCC
            Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
            JSESSIONID: A8940328DBEAD322317476C4BA4A7854
            JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
            passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
            data: '{"params":{"channel.CHANNEL_ID":"","channel.CHANNEL_NAME":"","channel.CHANNEL_KEY":"","channel.CHANNEL_TYPE":"","channel.CHANNEL_STATE":"0","channel.commonConfig.WORK_TIME_MODEL":"1","channel.commonConfig.BEGIN_WORK_TIME1":"08:30","channel.commonConfig.END_WORK_TIME1":"12:00","channel.commonConfig.BEGIN_WORK_TIME2":"13:30","channel.commonConfig.END_WORK_TIME2":"22:30","channel.commonConfig.BEGIN_WORK_TIME3":"11:11","channel.commonConfig.END_WORK_TIME3":"12:12","channel.commonConfig.OFFLINE_CHAT_TYPE":"INNER","channel.commonConfig.OFFLINE_CHAT_URL":"","channel.commonConfig.IS_REPLACE":"0","channel.commonConfig.HANDUP_IN_NEW_USER":"0","channel.commonConfig.NEW_FILE_SERVER_ADDR":"","channel.commonConfig.DEF_FILE_SERVER_ADDR":"","channel.commonConfig.NOTIFY_API_TYPE":"","channel.commonConfig.NOTIFY_API_URL":"","channel.commonConfig.NOTIFY_API_APPID":"","channel.commonConfig.NOTIFY_API_APPSECRET":"","channel.commonConfig.NOTIFY_API_APPID_AGENT":"","channel.commonConfig.NOTIFY_API_APPSECRET_AGENT":"","channel.commonConfig.NOTIFY_MSG_CLICK_URL":"","channel.commonConfig.NOTIFY_MSG_CLICK_PC_URL":"","channel.commonConfig.TEXT_TYPE":"0","channel.config.WECHAT_WECHAT_INFO_NAME":"","channel.config.WECHAT_WECHAT_INFO_ID":"","channel.config.WECHAT_APPKEY":"","channel.config.WECHAT_APPSERCET":"","channel.config.WECHAT_ACCESS_TOKEN":"","channel.config.WECHAT_ENCODEING_AESKEY":"","channel.config.WECHAT_WECHAT_ID":"","channel.config.WECHAT_MEDIAZK_SERVER_URL":"","channel.config.WECHAT_THIRDIM_GETTOKEN_URL":"","channel.config.WECHAT_THIRDIM_SERVER_TOKEN":"","channel.config.WECHAT_THIRDIM_REFRESHTOKEN_URL":"","channel.config.WECHAT_THIRDIM_UPDATESESSION_URL":"","channel.config.WECHAT_CUST_SERVICE_KEYWORD":"","channel.config.MINIAPP_WECHAT_INFO_NAME":"","channel.config.MINIAPP_WECHAT_INFO_ID":"","channel.config.MINIAPP_MINIAPP_appId":"","channel.config.MINIAPP_MINIAPP_appSecret":"","channel.config.MINIAPP_MINIAPP_token":"","channel.config.MINIAPP_MINIAPP_encodingAESKey":"","channel.config.MINIAPP_MINIAPP_getTokenService":"","channel.config.MINIAPP_MINIAPP_sendCustMsgService":"","channel.config.EP_WECHAT_WECHAT_INFO_NAME":"","channel.config.EP_WECHAT_WECHAT_INFO_ID":"","channel.config.EP_WECHAT_EP_WECHAT_APPKEY":"","channel.config.EP_WECHAT_EP_WECHAT_APPSERCET":"","channel.config.EP_WECHAT_EP_WECHAT_ACCESS_TOKEN":"","channel.config.EP_WECHAT_EP_WECHAT_ENCODEING_AESKEY":"","channel.config.EP_WECHAT_EP_WECHAT_AGENT_ID":"","channel.config.WECHATKF_WECHAT_INFO_NAME":"","channel.config.WECHATKF_WECHAT_INFO_ID":"","channel.config.WECHATKF_WECHATKF_WECHAT_APPKEY":"","channel.config.WECHATKF_WECHATKF_APPSERCET":"","channel.config.WECHATKF_WECHATKF_ACCESS_TOKEN":"","channel.config.WECHATKF_WECHATKF_ENCODEING_AESKEY":"","channel.config.TIKTOK_TIKTOK_EX_JSON":"","channel.config.TIKTOK_WECHAT_INFO_NAME":"","channel.config.TIKTOK_WECHAT_INFO_ID":"","channel.config.TIKTOK_TIKTOK_APPKEY":"","channel.config.TIKTOK_TIKTOK_APPSERCET":"","channel.config.TIKTOK_TIKTOK_ACCESS_TOKEN":"","channel.config.WEIBO_WEIBO_EX_JSON":"","channel.config.WEIBO_WECHAT_INFO_NAME":"","channel.config.WEIBO_WECHAT_INFO_ID":"","channel.config.WEIBO_WEIBO_APPKEY":"","channel.config.WEIBO_WEIBO_APPSERCET":"","channel.config.WEIBO_WEIBO_ACCESS_TOKEN":"","channel.config.FACEBOOK_FACEBOOK_EX_JSON":"","channel.config.FACEBOOK_WECHAT_INFO_NAME":"","channel.config.FACEBOOK_WECHAT_INFO_ID":"","channel.config.FACEBOOK_FACEBOOK_APPKEY":"","channel.config.FACEBOOK_FACEBOOK_APPSERCET":"","channel.config.FACEBOOK_FACEBOOK_ACCESS_TOKEN":"","channel.config.FEISHU_WECHAT_INFO_NAME":"","channel.config.FEISHU_WECHAT_INFO_ID":"","channel.config.FEISHU_FEISHU_APPKEY":"","channel.config.FEISHU_FEISHU_APPSERCET":"","channel.config.FEISHU_FEISHU_ACCESS_TOKEN":"","channel.config.FEISHU_FEISHU_ENCODEING_AESKEY":"","channel.config.FEISHU_FEISHU_MEDIAZK_SERVER_URL":"","channel.config.FEISHU_FEISHU_THIRDIM_GETTOKEN_URL":"","channel.config.FEISHU_FEISHU_THIRDIM_SERVER_TOKEN":"","channel.config.FEISHU_FEISHU_THIRDIM_REFRESHTOKEN_URL":"","channel.config.FEISHU_FEISHU_THIRDIM_UPDATESESSION_URL":"","channel.config.FEISHU_FEISHU_CUST_SERVICE_KEYWORD":"","channel.commonConfig.VIDEO_API_TYPE":"","channel.commonConfig.VIDEO_MINIPROGRAM_PAGE":"","channel.commonConfig.VIDEO_MINIPROGRAM_APPID":"","channel.commonConfig.VIDEO_MINIPROGRAM_THUMB_URL":"","channel.commonConfig.VIDEO_SATISFY_URL":"/yc-media/pages/satisfy/satisfy5.jsp?satisfyFlag=4","channel.commonConfig.VIDEO_HOLD":"0","channel.commonConfig.VIDEO_HOLD_FILE":"","channel.commonConfig.VIDEO_HOLD_FILE_NAME":"","channel.commonConfig.THIRD_CLIENT_URL":"/yc-media/pages/thirdClient.jsp","channel.commonConfig.THIRD_SMS_TEMP_ID":"thirdSmsTempId","channel.commonConfig.PRIORTIY_LATEST_AGENT":"0","channel.commonConfig.PRIORTIY_LATEST_AGENT_TYPE":"","channel.commonConfig.PRIORTIY_LATEST_DAY":"","channel.commonConfig.PRIORTIY_EXC_AGENT":"0","channel.commonConfig.AREA_CODES":"","channel.commonConfig.AREA_NAMES":"","channel.commonConfig.AGENT_GRAB":"0","channel.commonConfig.AGENT_GRAB_TIME":"60","channel.commonConfig.TRANS_BUSI_AGENT":"1","channel.commonConfig.TENANT_TRANSFER":"1","channel.commonConfig.OPEN_USER_SATISFY":"0","channel.commonConfig.OPEN_AGENT_SATISFY":"0","channel.commonConfig.OPEN_SATISFY":"0","channel.commonConfig.REPEAT_SATISFY":"0","channel.commonConfig.TIMEOUT_SATISFY":"0","channel.commonConfig.CC_MEDIA_SATISFY_NEWS_TITLE":"","channel.commonConfig.CC_MEDIA_SATISFY_NEWS_DESC":"","channel.commonConfig.CC_MEDIA_SATISFY_NEWS_URL":"","channel.commonConfig.CC_MEDIA_SATISFY_NEWS_PICURL":"","channel.commonConfig.ROBOT_SATISFY_FLAG":"0","channel.commonConfig.ROBOT_TOAGENT_SATISFY_FLAG":"1","channel.commonConfig.ROBOT_SATISFY_TYPE":"satisfy5","channel.commonConfig.ROBOT_SATISFY_TITLE":"机器人满意度评价","channel.commonConfig.ROBOT_SATISFY_DESC":"请对机器人本次服务进行评价","channel.commonConfig.ROBOT_SATISFY_URL":"/yc-media/pages/satisfy/robot-satisfy.jsp","channel.commonConfig.ROBOT_SATISFY_ICON":"","channel.commonConfig.WATERMARK":"","channel.commonConfig.HIGHLIGHTCHANNEL":"0","channel.commonConfig.SHOW_SATISFY":"0","channel.commonConfig.DISABLE_AGENT_SEND_FILE":"0","channel.commonConfig.DISABLE_AGENT_SEND_AUDIO":"0","channel.commonConfig.DISABLE_AGENT_SEND_VEDIO":"0","channel.commonConfig.DISABLE_AGENT_SEND_PIC":"0","channel.commonConfig.WEB_URL":"","channel.commonConfig.INTO_DEF_KEY_FLAG":"0","channel.commonConfig.SEND_ROBOT_WELCOME_FLAG":"0","channel.commonConfig.SKIP_ROBOT_WELCOME":"0","channel.commonConfig.KEYWORD_CHECK_FLAG":"0","channel.commonConfig.AGENT_CHECK_FLAG":"0","channel.commonConfig.OTHER_NOTICE_SERVICE_ID":"","channel.commonConfig.START_AGENT_SERVICE_ID":"","marsPrefix":"channel.","type":"","mars":"channel.getChannel"},"controls":["channel.getChannel","channel.getWorkTimeStyle","common.getDict(CC_BASE_CHANNEL_TYPE)","common.getDict(CC_MEDIA_AGENT_WOLCOME_TYPE)","common.getDict(CC_MEDIA_IMPORTANT_LEVEL)","common.getDict(CC_MEDIA_NOTINSERVICE_OPER_TYPE)","common.getDict(CC_MEDIA_QUEUE_RULE)","common.getDict(CC_MEDIA_ROUTING_RULE)","common.getDict(CC_MEDIA_SATISFY_TYPE)","common.getDict(CC_MEDIA_WELCOME_TYPE)","common.getDict(CC_MEDIA_WORD_TYPE)"]}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
    - name: "add_web_channel"
      describe: "新增网页渠道"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
            action: add
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "9536"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-media/pages/media/channel-edit.jsp?entId=
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
            token: bWFyc0AyMDE5
        cookies:
            HMACCOUNT: 028EBC18F491ADCC
            Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
            JSESSIONID: A8940328DBEAD322317476C4BA4A7854
            JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
            passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
            data: '{"channel.CHANNEL_ID":"","channel.CHANNEL_NAME":"web6004","channel.CHANNEL_KEY":"6001","channel.CHANNEL_TYPE":"1","channel.CHANNEL_STATE":"0","channel.commonConfig.WORK_TIME_MODEL":"1","channel.commonConfig.BEGIN_WORK_TIME1":"08:30","channel.commonConfig.END_WORK_TIME1":"12:00","channel.commonConfig.BEGIN_WORK_TIME2":"13:30","channel.commonConfig.END_WORK_TIME2":"22:30","channel.commonConfig.BEGIN_WORK_TIME3":"11:11","channel.commonConfig.END_WORK_TIME3":"12:12","channel.commonConfig.OFFLINE_CHAT_TYPE":"INNER","channel.commonConfig.OFFLINE_CHAT_URL":"","channel.commonConfig.IS_REPLACE":"0","channel.commonConfig.HANDUP_IN_NEW_USER":"0","channel.commonConfig.NEW_FILE_SERVER_ADDR":"","channel.commonConfig.DEF_FILE_SERVER_ADDR":"","channel.commonConfig.NOTIFY_API_TYPE":"","channel.commonConfig.NOTIFY_API_URL":"","channel.commonConfig.NOTIFY_API_APPID":"","channel.commonConfig.NOTIFY_API_APPSECRET":"","channel.commonConfig.NOTIFY_API_APPID_AGENT":"","channel.commonConfig.NOTIFY_API_APPSECRET_AGENT":"","channel.commonConfig.NOTIFY_MSG_CLICK_URL":"","channel.commonConfig.NOTIFY_MSG_CLICK_PC_URL":"","channel.commonConfig.TEXT_TYPE":"0","channel.commonConfig.WELCOME_TYPE":"welcome1","channel.commonConfig.AGENT_WOLCOME_TYPE":"awelcome1","channel.commonConfig.WORD_TYPE":"word1","channel.config.WECHAT_WECHAT_INFO_NAME":"","channel.config.WECHAT_WECHAT_INFO_ID":"","channel.config.WECHAT_APPKEY":"","channel.config.WECHAT_APPSERCET":"","channel.config.WECHAT_ACCESS_TOKEN":"","channel.config.WECHAT_ENCODEING_TYPE":"3","channel.config.WECHAT_ENCODEING_AESKEY":"","channel.config.WECHAT_ACCESSTOKEN_MODE":"0","channel.config.WECHAT_WECHAT_ID":"","channel.config.WECHAT_SEND_CUST_MSG_TYPE":"1","channel.config.WECHAT_MEDIAZK_SERVER_URL":"","channel.config.WECHAT_THIRDIM_GETTOKEN_URL":"","channel.config.WECHAT_THIRDIM_SERVER_TOKEN":"","channel.config.WECHAT_THIRDIM_REFRESHTOKEN_URL":"","channel.config.WECHAT_THIRDIM_UPDATESESSION_URL":"","channel.config.WECHAT_CUST_SERVICE_KEYWORD":"","channel.config.MINIAPP_WECHAT_INFO_NAME":"","channel.config.MINIAPP_WECHAT_INFO_ID":"","channel.config.MINIAPP_MINIAPP_appId":"","channel.config.MINIAPP_MINIAPP_appSecret":"","channel.config.MINIAPP_MINIAPP_token":"","channel.config.MINIAPP_MINIAPP_encodingAESKey":"","channel.config.MINIAPP_MINIAPP_getTokenType":"1","channel.config.MINIAPP_MINIAPP_sendCustMsgType":"1","channel.config.MINIAPP_MINIAPP_getTokenService":"","channel.config.MINIAPP_MINIAPP_sendCustMsgService":"","channel.config.EP_WECHAT_WECHAT_INFO_NAME":"","channel.config.EP_WECHAT_WECHAT_INFO_ID":"","channel.config.EP_WECHAT_EP_WECHAT_APPKEY":"","channel.config.EP_WECHAT_EP_WECHAT_APPSERCET":"","channel.config.EP_WECHAT_EP_WECHAT_ACCESS_TOKEN":"","channel.config.EP_WECHAT_EP_WECHAT_ENCODEING_AESKEY":"","channel.config.EP_WECHAT_EP_WECHAT_AGENT_ID":"","channel.config.WECHATKF_WECHAT_INFO_NAME":"","channel.config.WECHATKF_WECHAT_INFO_ID":"","channel.config.WECHATKF_WECHATKF_WECHAT_APPKEY":"","channel.config.WECHATKF_WECHATKF_APPSERCET":"","channel.config.WECHATKF_WECHATKF_ACCESS_TOKEN":"","channel.config.WECHATKF_WECHATKF_ENCODEING_AESKEY":"","channel.config.TIKTOK_TIKTOK_EX_JSON":"","channel.config.TIKTOK_WECHAT_INFO_NAME":"","channel.config.TIKTOK_WECHAT_INFO_ID":"","channel.config.TIKTOK_TIKTOK_APPKEY":"","channel.config.TIKTOK_TIKTOK_APPSERCET":"","channel.config.TIKTOK_TIKTOK_ACCESS_TOKEN":"","channel.config.WEIBO_WEIBO_EX_JSON":"","channel.config.WEIBO_WECHAT_INFO_NAME":"","channel.config.WEIBO_WECHAT_INFO_ID":"","channel.config.WEIBO_WEIBO_APPKEY":"","channel.config.WEIBO_WEIBO_APPSERCET":"","channel.config.WEIBO_WEIBO_ACCESS_TOKEN":"","channel.config.FACEBOOK_FACEBOOK_EX_JSON":"","channel.config.FACEBOOK_WECHAT_INFO_NAME":"","channel.config.FACEBOOK_WECHAT_INFO_ID":"","channel.config.FACEBOOK_FACEBOOK_APPKEY":"","channel.config.FACEBOOK_FACEBOOK_APPSERCET":"","channel.config.FACEBOOK_FACEBOOK_ACCESS_TOKEN":"","channel.config.FEISHU_WECHAT_INFO_NAME":"","channel.config.FEISHU_WECHAT_INFO_ID":"","channel.config.FEISHU_FEISHU_APPKEY":"","channel.config.FEISHU_FEISHU_APPSERCET":"","channel.config.FEISHU_FEISHU_ACCESS_TOKEN":"","channel.config.FEISHU_FEISHU_ENCODEING_TYPE":"","channel.config.FEISHU_FEISHU_ENCODEING_AESKEY":"","channel.config.FEISHU_FEISHU_ACCESSTOKEN_MODE":"","channel.config.FEISHU_FEISHU_SEND_CUST_MSG_TYPE":"1","channel.config.FEISHU_FEISHU_MEDIAZK_SERVER_URL":"","channel.config.FEISHU_FEISHU_THIRDIM_GETTOKEN_URL":"","channel.config.FEISHU_FEISHU_THIRDIM_SERVER_TOKEN":"","channel.config.FEISHU_FEISHU_THIRDIM_REFRESHTOKEN_URL":"","channel.config.FEISHU_FEISHU_THIRDIM_UPDATESESSION_URL":"","channel.config.FEISHU_FEISHU_CUST_SERVICE_KEYWORD":"","channel.commonConfig.VIDEO_API_TYPE":"","channel.commonConfig.VIDEO_MINIPROGRAM_PAGE":"","channel.commonConfig.VIDEO_MINIPROGRAM_APPID":"","channel.commonConfig.VIDEO_MINIPROGRAM_THUMB_URL":"","channel.commonConfig.VIDEO_SATISFY_URL":"/yc-media/pages/satisfy/satisfy5.jsp?satisfyFlag=4","channel.commonConfig.VIDEO_HOLD":"0","channel.commonConfig.VIDEO_HOLD_FILE":"","channel.commonConfig.VIDEO_HOLD_FILE_NAME":"","channel.commonConfig.THIRD_CLIENT_URL":"/yc-media/pages/thirdClient.jsp","channel.commonConfig.THIRD_SMS_TEMP_ID":"thirdSmsTempId","channel.commonConfig.PRIORTIY_LATEST_AGENT":"0","channel.commonConfig.PRIORTIY_LATEST_AGENT_TYPE":"","channel.commonConfig.PRIORTIY_LATEST_DAY":"","channel.commonConfig.PRIORTIY_EXC_AGENT":"0","channel.commonConfig.ROUTING_RULE":"1","channel.commonConfig.QUEUE_RULE":"1","channel.commonConfig.AREA_CODES":"","channel.commonConfig.AREA_NAMES":"","channel.commonConfig.SYSTEM_NOTINSERVICE_OPER_TYPE":"1","channel.commonConfig.AGENT_GRAB":"0","channel.commonConfig.AGENT_GRAB_TIME":"60","channel.commonConfig.TRANS_BUSI_AGENT":"1","channel.commonConfig.TENANT_TRANSFER":"1","channel.commonConfig.OPEN_USER_SATISFY":"0","channel.commonConfig.OPEN_AGENT_SATISFY":"0","channel.commonConfig.OPEN_SATISFY":"0","channel.commonConfig.REPEAT_SATISFY":"0","channel.commonConfig.TIMEOUT_SATISFY":"0","channel.commonConfig.SATISFY_TYPE":"satisfy1","channel.commonConfig.CC_MEDIA_SATISFY_NEWS_TITLE":"","channel.commonConfig.CC_MEDIA_SATISFY_NEWS_DESC":"","channel.commonConfig.CC_MEDIA_SATISFY_NEWS_URL":"","channel.commonConfig.CC_MEDIA_SATISFY_NEWS_PICURL":"","channel.commonConfig.ROBOT_SATISFY_FLAG":"0","channel.commonConfig.ROBOT_TOAGENT_SATISFY_FLAG":"1","channel.commonConfig.ROBOT_SATISFY_TYPE":"satisfy5","channel.commonConfig.ROBOT_SATISFY_TITLE":"机器人满意度评价","channel.commonConfig.ROBOT_SATISFY_DESC":"请对机器人本次服务进行评价","channel.commonConfig.ROBOT_SATISFY_URL":"/yc-media/pages/satisfy/robot-satisfy.jsp","channel.commonConfig.ROBOT_SATISFY_ICON":"","channel.commonConfig.WATERMARK":"","channel.commonConfig.HIGHLIGHTCHANNEL":"0","channel.commonConfig.SHOW_SATISFY":"0","channel.commonConfig.DISABLE_AGENT_SEND_FILE":"0","channel.commonConfig.DISABLE_AGENT_SEND_AUDIO":"0","channel.commonConfig.DISABLE_AGENT_SEND_VEDIO":"0","channel.commonConfig.DISABLE_AGENT_SEND_PIC":"0","channel.commonConfig.WEB_URL":"http://**************:9060","channel.commonConfig.IMPORTANT_LEVEL":"1","channel.commonConfig.INTO_DEF_KEY_FLAG":"0","channel.commonConfig.SEND_ROBOT_WELCOME_FLAG":"0","channel.commonConfig.SKIP_ROBOT_WELCOME":"0","channel.commonConfig.KEYWORD_CHECK_FLAG":"0","channel.commonConfig.AGENT_CHECK_FLAG":"0","channel.commonConfig.OTHER_NOTICE_SERVICE_ID":"","channel.commonConfig.START_AGENT_SERVICE_ID":""}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 添加成功！
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "get_channelList"
      describe: "渠道管理列表"
      request:
        method: POST
        url: /cc-media/webcall
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "305"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-media/pages/media/channel-list.jsp
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
        cookies:
            HMACCOUNT: 028EBC18F491ADCC
            Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
            JSESSIONID: A8940328DBEAD322317476C4BA4A7854
            JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
            passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
            data: '{"params":{"channelName":"","channelKey":"","channelType":"","channelState":"","toggle":""},"controls":["channel.list","common.getDict(CC_BASE_CHANNEL_STATUS)","common.getDict(CC_BASE_CHANNEL_TYPE)"]}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
    - name: ""
      request:
        method: POST
        url: /cc-media/webcall
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "14155"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-media/pages/media/channel-auto-config-form.jsp?entId=&channelId=82496131467827722378754&channelName=web6004&textType=0&channelType=1&channelKey=6001
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
        cookies:
            HMACCOUNT: 028EBC18F491ADCC
            Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
            JSESSIONID: A8940328DBEAD322317476C4BA4A7854
            JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
            passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
            data: '{"params":{"entId":"","channelId":"82496131467827722378754","channelKey":"6001","channelType":"1","autoReplyConfig.WELCOME_MSG":"","autoReplyConfig.ADVERT_MSG":"","autoReplyConfig.AGENT_TIMEOUT":"5","autoReplyConfig.AGENT_TIMEOUT_TRANSFER":"0","autoReplyConfig.AGENT_TIMEOUT_MSG":"我现在临时有事需要离开电脑前，您可以先浏览一下网站，或者留下您的联系方式等我回复，给您带来的不便请多谅解。","autoReplyConfig.VISITOR_TIMEOUT":"5","autoReplyConfig.VISITOR_TIMEOUT_MSG":"您好，请问您还在线吗？如随后#time#分钟仍未收到您的消息，本次会话将暂时结束；如您的问题已解决，输入88可结束本次会话。","autoReplyConfig.USER_STOP_CHAT_MSG":"用户已主动断开会话","autoReplyConfig.AUTO_CLOSESESSION_TIMEOUT":"5","autoReplyConfig.AUTO_CLOSESESSION_MSG":"系统自动关闭对话","autoReplyConfig.CHANNEL_STOP_MSG":"当前渠道已停用，无法提供服务","autoReplyConfig.QUEUE_NOTIFY_TIME":"5","autoReplyConfig.VISITOR_QUEUE_MSG":"您好，客服繁忙，您当前的排队号为#sortPos#,输入88可取消当前排队。","autoReplyConfig.VISITOR_QUEUE_IN_MSG":"您当前正在排队，请耐心等待，输入88可取消当前排队。","autoReplyConfig.VISITOR_CANCELQUE_MSG":"您好，由于当前系统排队人数过多，给您带来的不便，敬请原谅，请稍后再试。","autoReplyConfig.QUEUE_MAX_COUNT":"200","autoReplyConfig.REALTIME_NOTIFY_QUEUE_NO":"0","autoReplyConfig.NOTIFY_NEW_QUEUE_NO":"1","autoReplyConfig.QUEUE_FULL_MSG":"您好，当前排队人数已达系统上限，请稍后再试。","autoReplyConfig.VISITOR_QUEUE_TIMEOUT":"5","autoReplyConfig.QUEUE_TIMEOUT_MSG":"您好，当前排队已超时，请稍后再试。","autoReplyConfig.AGENT_GRAB_MSG":"","autoReplyConfig.REPEATE_TOAGENT_MSG":"正在接入人工中,请等待.","autoReplyConfig.VISITOR_ACCESS_ROBOT_MSG":"请一句话描述您的问题，我们来帮您解决并转到合适的人工服务。","autoReplyConfig.ROBOT_TIME_OUT_TIME":"5","autoReplyConfig.ROBOT_TIME_OUT_MSG":"您好，当前机器人会话已超时，感谢你的咨询。","autoReplyConfig.ROBOT_PIC_MSG_OPER":"1","autoReplyConfig.ROBOT_PIC_MSG_REPLY":"","autoReplyConfig.ROBOT_EN_MSG_OPER":"0","autoReplyConfig.ROBOT_EN_MSG_REPLY":"","autoReplyConfig.ROBOT_EXP_MSG_OPER":"0","autoReplyConfig.ROBOT_EXP_MSG_REPLY":"","autoReplyConfig.ROBOT_SYMBOL_MSG_OPER":"0","autoReplyConfig.ROBOT_SYMBOL_MSG_REPLY":"","autoReplyConfig.ROBOT_AUDIO_MSG_OPER":"0","autoReplyConfig.ROBOT_AUDIO_MSG_REPLY":"","autoReplyConfig.ROBOT_ERROR_REPLY":"","autoReplyConfig.ROBOT_NO_CHANNLE_REPLY":"当前渠道还未开通服务,敬请期待~","autoReplyConfig.ROBOT_NOT_TEXT_REPLY":"不好意思，我还在学习，暂时理解不了您说的问题!","autoReplyConfig.ROBOT_NO_CONTENT_REPLY":"请输入咨询的内容~","autoReplyConfig.ROBOT_NOT_INIT_REPLY":"当前服务正在维护中,请稍后再试~","autoReplyConfig.ROBOT_NOT_SUPPORT_NEWS_REPLY":"对不起，我目前只能回答常见的业务相关问题！此问题暂不在我知识范围内，我会继续努力学习的！您也可以换个简单的问法向我提问，或许我就可以回答您了...","autoReplyConfig.ROBOT_MEDIA_WELCOME":"您好，将为您接入人工在线客服","autoReplyConfig.ROBOT_NO_ANSWER_REPLY":"暂时还不能理解您的问题,可以换个其他问题试试~","autoReplyConfig.ROBOT_RECOMMENDED_BEFORWORD":"请问您想了解的是以下哪项业务?","autoReplyConfig.ROBOT_RECOMMENDED_AFTERWORD":"请输入您要了解的业务。","autoReplyConfig.ROBOT_KEYWORD_TOAGENT_REPLY":"欢迎接入人工在线客服","autoReplyConfig.ROBOT_KEYWORD_FORBIT_TOAGENT_REPLY":"您好，暂时无法接入人工客服","autoReplyConfig.ROBOT_BEYOND_LIMIT_REPLY":"","autoReplyConfig.ROBOT_TRANSLATION_SWITCH":"0","autoReplyConfig.INVITE_NO_AGENT_NOTIFY":"0","autoReplyConfig.INVITE_NO_AGENT_NOTIFY_MSG":"","autoReplyConfig.CUST_TRANSLATION_SWITCH":"0","autoReplyConfig.VISITOR_ACCESS_MSG":"您好，我是客服#agentName#，请问有什么可以帮您的？输入88可结束本次会话。","autoReplyConfig.TRANSFER_CHAT_TO_MSG":"将会话转接给坐席#agentName#（#agentNickName#）","autoReplyConfig.TRANSFER_CHAT_GET_MSG":"来自坐席#agentName#（#agentNickName#）","autoReplyConfig.TRANSFER_CHAT_CUST_MSG":"","autoReplyConfig.ACTIVE_STOP_WORDS":"","autoReplyConfig.MAX_SEND_MSG_COUNT":"50","autoReplyConfig.MAX_SEND_MSG_COUNT_MSG":"发言过于频繁，请您耐心等待！","autoReplyConfig.MAX_INNER_TIME":"50","autoReplyConfig.MAX_INNER_COUNT":"50","autoReplyConfig.MAX_INNER_STOP_TIME":"50","autoReplyConfig.MAX_INNER_STOP_MSG":"请求过于频繁，请稍后再试！","autoReplyConfig.AGENT_BUSY_MSG":"您好，当前客服繁忙，请稍后再试。","autoReplyConfig.AGENT_MAX_SERVER_MSG":"您好，当前客服服务人数过多，请稍后再试。","autoReplyConfig.AGENT_OFFLINE_MSG":"您好，当前无客服在线，请稍后再试。","autoReplyConfig.UPDATE_LAST_SATISFY_KEYWORD":"XGMYD","autoReplyConfig.UPDATE_LAST_SATISFY_TIMEOUT_TIME":"30","autoReplyConfig.UPDATE_LAST_SATISFY_MSG":"","autoReplyConfig.SATISFY_USER_MSG_Y":"感谢您的评价,祝您生活愉快~","autoReplyConfig.NOT_SATISFY_USER_MSG_Y":"您的评价我们已收到,祝您生活愉快~","autoReplyConfig.SATISFY_TIME_OUT_TIME":"5","autoReplyConfig.SATISFY_TIME_OUT_MSG":"评价已过期","autoReplyConfig.SATISFY_USER_MSG_N":"您的评价我们已收到，祝您生活愉快~","autoReplyConfig.UPDATE_SATISFY_SUCC_MSG":"感谢您再次评价","autoReplyConfig.UPDATE_SATISFY_TIMEOUT_MSG":"评价已过期","autoReplyConfig.WORD_FORM_URL":"","autoReplyConfig.SYSTEM_NOTINSERVICE_MSG":"您好，当前为系统非工作时间，服务时间为#serviceTime#，您可以写下留言，我们会尽快跟进处理，输入88结束留言。","autoReplyConfig.H5_CKCODE_TIMEOUT":"10","autoReplyConfig.H5_CKCODE_FAIL_CONTENT":"","autoReplyConfig.CUST_SENSITIVE_REPLY":"您输入的信息中包含敏感词:#sensitiveWord#","autoReplyConfig.INVITATION_CODE_TIMEOUT":"30","autoReplyConfig.INVITE_SMS_MSG":"客服#agentNickName#邀请你在线聊天，请点击链接||https://ip:port/yc-media/pages/web-chat.jsp?channelKey=#channelKey#&message=#CODE_MSG#||，有效期为：#time#","autoReplyConfig.BLACKLIST_MSG":"您好，当前系统不方便接入，请稍后再试 。","autoReplyConfig.WORD_TIME_OUT_TIME":"5","autoReplyConfig.WORD_TIME_OUT_MSG":"您好，当前留言已超时，感谢你的留言。","autoReplyConfig.WORD_END_MSG":"感谢您的留言，我们会尽快跟进处理！","autoReplyConfig.GOODBYE_MSG":"您好，本次会话结束，感谢您使用我们的系统，再见。","autoReplyConfig.INTO_WORD_MSG":"您可以写下留言，我们会尽快跟进处理，输入“88”结束留言","autoReplyConfig.NOT_KEYWORD_MSG":"未命中关键字，请在回复语中配置“未命中关键字的提示语”，并添加相关关键字","autoReplyConfig.ROBOT_TRANSLATION_INFO_ID":"1","autoReplyConfig.FOLLOW_MSG":"","marsPrefix":"autoReplyConfig.","mars":"channel.getAutoConfig"},"controls":["channel.getAutoConfig","common.getDict(LANGUAGES)","ttcInfo.dictList"]}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
    - name: "add_web_autoConfig"
      describe: "保存网页渠道自动回复语"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
            action: autoConfig
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "19846"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-media/pages/media/channel-auto-config-form.jsp?entId=&channelId=82496131467827722378754&channelName=web6004&textType=0&channelType=1&channelKey=6001
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
            token: bWFyc0AyMDE5
        cookies:
            HMACCOUNT: 028EBC18F491ADCC
            Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
            JSESSIONID: A8940328DBEAD322317476C4BA4A7854
            JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
            passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
            data: '{"entId":"","channelId":"82496131467827722378754","channelKey":"6001","channelType":"1","autoReplyConfig.WELCOME_MSG":"%E8%BD%AC%E4%BA%BA%E5%B7%A5%E8%AF%B7%E6%8C%891","autoReplyConfig.ADVERT_MSG":"","autoReplyConfig.AGENT_TIMEOUT":"5","autoReplyConfig.AGENT_TIMEOUT_TRANSFER":"0","autoReplyConfig.AGENT_TIMEOUT_MSG":"%E6%88%91%E7%8E%B0%E5%9C%A8%E4%B8%B4%E6%97%B6%E6%9C%89%E4%BA%8B%E9%9C%80%E8%A6%81%E7%A6%BB%E5%BC%80%E7%94%B5%E8%84%91%E5%89%8D%EF%BC%8C%E6%82%A8%E5%8F%AF%E4%BB%A5%E5%85%88%E6%B5%8F%E8%A7%88%E4%B8%80%E4%B8%8B%E7%BD%91%E7%AB%99%EF%BC%8C%E6%88%96%E8%80%85%E7%95%99%E4%B8%8B%E6%82%A8%E7%9A%84%E8%81%94%E7%B3%BB%E6%96%B9%E5%BC%8F%E7%AD%89%E6%88%91%E5%9B%9E%E5%A4%8D%EF%BC%8C%E7%BB%99%E6%82%A8%E5%B8%A6%E6%9D%A5%E7%9A%84%E4%B8%8D%E4%BE%BF%E8%AF%B7%E5%A4%9A%E8%B0%85%E8%A7%A3%E3%80%82","autoReplyConfig.VISITOR_TIMEOUT":"5","autoReplyConfig.VISITOR_TIMEOUT_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E8%AF%B7%E9%97%AE%E6%82%A8%E8%BF%98%E5%9C%A8%E7%BA%BF%E5%90%97%EF%BC%9F%E5%A6%82%E9%9A%8F%E5%90%8E%23time%23%E5%88%86%E9%92%9F%E4%BB%8D%E6%9C%AA%E6%94%B6%E5%88%B0%E6%82%A8%E7%9A%84%E6%B6%88%E6%81%AF%EF%BC%8C%E6%9C%AC%E6%AC%A1%E4%BC%9A%E8%AF%9D%E5%B0%86%E6%9A%82%E6%97%B6%E7%BB%93%E6%9D%9F%EF%BC%9B%E5%A6%82%E6%82%A8%E7%9A%84%E9%97%AE%E9%A2%98%E5%B7%B2%E8%A7%A3%E5%86%B3%EF%BC%8C%E8%BE%93%E5%85%A588%E5%8F%AF%E7%BB%93%E6%9D%9F%E6%9C%AC%E6%AC%A1%E4%BC%9A%E8%AF%9D%E3%80%82","autoReplyConfig.USER_STOP_CHAT_MSG":"%E7%94%A8%E6%88%B7%E5%B7%B2%E4%B8%BB%E5%8A%A8%E6%96%AD%E5%BC%80%E4%BC%9A%E8%AF%9D","autoReplyConfig.AUTO_CLOSESESSION_TIMEOUT":"5","autoReplyConfig.AUTO_CLOSESESSION_MSG":"%E7%B3%BB%E7%BB%9F%E8%87%AA%E5%8A%A8%E5%85%B3%E9%97%AD%E5%AF%B9%E8%AF%9D","autoReplyConfig.CHANNEL_STOP_MSG":"%E5%BD%93%E5%89%8D%E6%B8%A0%E9%81%93%E5%B7%B2%E5%81%9C%E7%94%A8%EF%BC%8C%E6%97%A0%E6%B3%95%E6%8F%90%E4%BE%9B%E6%9C%8D%E5%8A%A1","autoReplyConfig.QUEUE_NOTIFY_TIME":"5","autoReplyConfig.VISITOR_QUEUE_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%AE%A2%E6%9C%8D%E7%B9%81%E5%BF%99%EF%BC%8C%E6%82%A8%E5%BD%93%E5%89%8D%E7%9A%84%E6%8E%92%E9%98%9F%E5%8F%B7%E4%B8%BA%23sortPos%23%2C%E8%BE%93%E5%85%A588%E5%8F%AF%E5%8F%96%E6%B6%88%E5%BD%93%E5%89%8D%E6%8E%92%E9%98%9F%E3%80%82","autoReplyConfig.VISITOR_QUEUE_IN_MSG":"%E6%82%A8%E5%BD%93%E5%89%8D%E6%AD%A3%E5%9C%A8%E6%8E%92%E9%98%9F%EF%BC%8C%E8%AF%B7%E8%80%90%E5%BF%83%E7%AD%89%E5%BE%85%EF%BC%8C%E8%BE%93%E5%85%A588%E5%8F%AF%E5%8F%96%E6%B6%88%E5%BD%93%E5%89%8D%E6%8E%92%E9%98%9F%E3%80%82","autoReplyConfig.VISITOR_CANCELQUE_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E7%94%B1%E4%BA%8E%E5%BD%93%E5%89%8D%E7%B3%BB%E7%BB%9F%E6%8E%92%E9%98%9F%E4%BA%BA%E6%95%B0%E8%BF%87%E5%A4%9A%EF%BC%8C%E7%BB%99%E6%82%A8%E5%B8%A6%E6%9D%A5%E7%9A%84%E4%B8%8D%E4%BE%BF%EF%BC%8C%E6%95%AC%E8%AF%B7%E5%8E%9F%E8%B0%85%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%E3%80%82","autoReplyConfig.QUEUE_MAX_COUNT":"200","autoReplyConfig.REALTIME_NOTIFY_QUEUE_NO":"0","autoReplyConfig.NOTIFY_NEW_QUEUE_NO":"1","autoReplyConfig.QUEUE_FULL_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E6%8E%92%E9%98%9F%E4%BA%BA%E6%95%B0%E5%B7%B2%E8%BE%BE%E7%B3%BB%E7%BB%9F%E4%B8%8A%E9%99%90%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%E3%80%82","autoReplyConfig.VISITOR_QUEUE_TIMEOUT":"5","autoReplyConfig.QUEUE_TIMEOUT_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E6%8E%92%E9%98%9F%E5%B7%B2%E8%B6%85%E6%97%B6%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%E3%80%82","autoReplyConfig.AGENT_GRAB_MSG":"","autoReplyConfig.REPEATE_TOAGENT_MSG":"%E6%AD%A3%E5%9C%A8%E6%8E%A5%E5%85%A5%E4%BA%BA%E5%B7%A5%E4%B8%AD%2C%E8%AF%B7%E7%AD%89%E5%BE%85.","autoReplyConfig.VISITOR_ACCESS_ROBOT_MSG":"%E8%AF%B7%E4%B8%80%E5%8F%A5%E8%AF%9D%E6%8F%8F%E8%BF%B0%E6%82%A8%E7%9A%84%E9%97%AE%E9%A2%98%EF%BC%8C%E6%88%91%E4%BB%AC%E6%9D%A5%E5%B8%AE%E6%82%A8%E8%A7%A3%E5%86%B3%E5%B9%B6%E8%BD%AC%E5%88%B0%E5%90%88%E9%80%82%E7%9A%84%E4%BA%BA%E5%B7%A5%E6%9C%8D%E5%8A%A1%E3%80%82","autoReplyConfig.ROBOT_TIME_OUT_TIME":"5","autoReplyConfig.ROBOT_TIME_OUT_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E6%9C%BA%E5%99%A8%E4%BA%BA%E4%BC%9A%E8%AF%9D%E5%B7%B2%E8%B6%85%E6%97%B6%EF%BC%8C%E6%84%9F%E8%B0%A2%E4%BD%A0%E7%9A%84%E5%92%A8%E8%AF%A2%E3%80%82","autoReplyConfig.ROBOT_PIC_MSG_OPER":"1","autoReplyConfig.ROBOT_PIC_MSG_REPLY":"","autoReplyConfig.ROBOT_EN_MSG_OPER":"0","autoReplyConfig.ROBOT_EN_MSG_REPLY":"","autoReplyConfig.ROBOT_EXP_MSG_OPER":"0","autoReplyConfig.ROBOT_EXP_MSG_REPLY":"","autoReplyConfig.ROBOT_SYMBOL_MSG_OPER":"0","autoReplyConfig.ROBOT_SYMBOL_MSG_REPLY":"","autoReplyConfig.ROBOT_AUDIO_MSG_OPER":"0","autoReplyConfig.ROBOT_AUDIO_MSG_REPLY":"","autoReplyConfig.ROBOT_ERROR_REPLY":"","autoReplyConfig.ROBOT_NO_CHANNLE_REPLY":"%E5%BD%93%E5%89%8D%E6%B8%A0%E9%81%93%E8%BF%98%E6%9C%AA%E5%BC%80%E9%80%9A%E6%9C%8D%E5%8A%A1%2C%E6%95%AC%E8%AF%B7%E6%9C%9F%E5%BE%85~","autoReplyConfig.ROBOT_NOT_TEXT_REPLY":"%E4%B8%8D%E5%A5%BD%E6%84%8F%E6%80%9D%EF%BC%8C%E6%88%91%E8%BF%98%E5%9C%A8%E5%AD%A6%E4%B9%A0%EF%BC%8C%E6%9A%82%E6%97%B6%E7%90%86%E8%A7%A3%E4%B8%8D%E4%BA%86%E6%82%A8%E8%AF%B4%E7%9A%84%E9%97%AE%E9%A2%98!","autoReplyConfig.ROBOT_NO_CONTENT_REPLY":"%E8%AF%B7%E8%BE%93%E5%85%A5%E5%92%A8%E8%AF%A2%E7%9A%84%E5%86%85%E5%AE%B9~","autoReplyConfig.ROBOT_NOT_INIT_REPLY":"%E5%BD%93%E5%89%8D%E6%9C%8D%E5%8A%A1%E6%AD%A3%E5%9C%A8%E7%BB%B4%E6%8A%A4%E4%B8%AD%2C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95~","autoReplyConfig.ROBOT_NOT_SUPPORT_NEWS_REPLY":"%E5%AF%B9%E4%B8%8D%E8%B5%B7%EF%BC%8C%E6%88%91%E7%9B%AE%E5%89%8D%E5%8F%AA%E8%83%BD%E5%9B%9E%E7%AD%94%E5%B8%B8%E8%A7%81%E7%9A%84%E4%B8%9A%E5%8A%A1%E7%9B%B8%E5%85%B3%E9%97%AE%E9%A2%98%EF%BC%81%E6%AD%A4%E9%97%AE%E9%A2%98%E6%9A%82%E4%B8%8D%E5%9C%A8%E6%88%91%E7%9F%A5%E8%AF%86%E8%8C%83%E5%9B%B4%E5%86%85%EF%BC%8C%E6%88%91%E4%BC%9A%E7%BB%A7%E7%BB%AD%E5%8A%AA%E5%8A%9B%E5%AD%A6%E4%B9%A0%E7%9A%84%EF%BC%81%E6%82%A8%E4%B9%9F%E5%8F%AF%E4%BB%A5%E6%8D%A2%E4%B8%AA%E7%AE%80%E5%8D%95%E7%9A%84%E9%97%AE%E6%B3%95%E5%90%91%E6%88%91%E6%8F%90%E9%97%AE%EF%BC%8C%E6%88%96%E8%AE%B8%E6%88%91%E5%B0%B1%E5%8F%AF%E4%BB%A5%E5%9B%9E%E7%AD%94%E6%82%A8%E4%BA%86...","autoReplyConfig.ROBOT_MEDIA_WELCOME":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%B0%86%E4%B8%BA%E6%82%A8%E6%8E%A5%E5%85%A5%E4%BA%BA%E5%B7%A5%E5%9C%A8%E7%BA%BF%E5%AE%A2%E6%9C%8D","autoReplyConfig.ROBOT_NO_ANSWER_REPLY":"%E6%9A%82%E6%97%B6%E8%BF%98%E4%B8%8D%E8%83%BD%E7%90%86%E8%A7%A3%E6%82%A8%E7%9A%84%E9%97%AE%E9%A2%98%2C%E5%8F%AF%E4%BB%A5%E6%8D%A2%E4%B8%AA%E5%85%B6%E4%BB%96%E9%97%AE%E9%A2%98%E8%AF%95%E8%AF%95~","autoReplyConfig.ROBOT_RECOMMENDED_BEFORWORD":"%E8%AF%B7%E9%97%AE%E6%82%A8%E6%83%B3%E4%BA%86%E8%A7%A3%E7%9A%84%E6%98%AF%E4%BB%A5%E4%B8%8B%E5%93%AA%E9%A1%B9%E4%B8%9A%E5%8A%A1%3F","autoReplyConfig.ROBOT_RECOMMENDED_AFTERWORD":"%E8%AF%B7%E8%BE%93%E5%85%A5%E6%82%A8%E8%A6%81%E4%BA%86%E8%A7%A3%E7%9A%84%E4%B8%9A%E5%8A%A1%E3%80%82","autoReplyConfig.ROBOT_KEYWORD_TOAGENT_REPLY":"%E6%AC%A2%E8%BF%8E%E6%8E%A5%E5%85%A5%E4%BA%BA%E5%B7%A5%E5%9C%A8%E7%BA%BF%E5%AE%A2%E6%9C%8D","autoReplyConfig.ROBOT_KEYWORD_FORBIT_TOAGENT_REPLY":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E6%9A%82%E6%97%B6%E6%97%A0%E6%B3%95%E6%8E%A5%E5%85%A5%E4%BA%BA%E5%B7%A5%E5%AE%A2%E6%9C%8D","autoReplyConfig.ROBOT_BEYOND_LIMIT_REPLY":"","autoReplyConfig.ROBOT_TRANSLATION_SWITCH":"0","autoReplyConfig.INVITE_NO_AGENT_NOTIFY":"0","autoReplyConfig.INVITE_NO_AGENT_NOTIFY_MSG":"","autoReplyConfig.CUST_TRANSLATION_SWITCH":"0","autoReplyConfig.VISITOR_ACCESS_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E6%88%91%E6%98%AF%E5%AE%A2%E6%9C%8D%23agentName%23%EF%BC%8C%E8%AF%B7%E9%97%AE%E6%9C%89%E4%BB%80%E4%B9%88%E5%8F%AF%E4%BB%A5%E5%B8%AE%E6%82%A8%E7%9A%84%EF%BC%9F%E8%BE%93%E5%85%A588%E5%8F%AF%E7%BB%93%E6%9D%9F%E6%9C%AC%E6%AC%A1%E4%BC%9A%E8%AF%9D%E3%80%82","autoReplyConfig.TRANSFER_CHAT_TO_MSG":"%E5%B0%86%E4%BC%9A%E8%AF%9D%E8%BD%AC%E6%8E%A5%E7%BB%99%E5%9D%90%E5%B8%AD%23agentName%23%EF%BC%88%23agentNickName%23%EF%BC%89","autoReplyConfig.TRANSFER_CHAT_GET_MSG":"%E6%9D%A5%E8%87%AA%E5%9D%90%E5%B8%AD%23agentName%23%EF%BC%88%23agentNickName%23%EF%BC%89","autoReplyConfig.TRANSFER_CHAT_CUST_MSG":"","autoReplyConfig.ACTIVE_STOP_WORDS":"","autoReplyConfig.MAX_SEND_MSG_COUNT":"50","autoReplyConfig.MAX_SEND_MSG_COUNT_MSG":"%E5%8F%91%E8%A8%80%E8%BF%87%E4%BA%8E%E9%A2%91%E7%B9%81%EF%BC%8C%E8%AF%B7%E6%82%A8%E8%80%90%E5%BF%83%E7%AD%89%E5%BE%85%EF%BC%81","autoReplyConfig.MAX_INNER_TIME":"50","autoReplyConfig.MAX_INNER_COUNT":"50","autoReplyConfig.MAX_INNER_STOP_TIME":"50","autoReplyConfig.MAX_INNER_STOP_MSG":"%E8%AF%B7%E6%B1%82%E8%BF%87%E4%BA%8E%E9%A2%91%E7%B9%81%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%EF%BC%81","autoReplyConfig.AGENT_BUSY_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E5%AE%A2%E6%9C%8D%E7%B9%81%E5%BF%99%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%E3%80%82","autoReplyConfig.AGENT_MAX_SERVER_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E5%AE%A2%E6%9C%8D%E6%9C%8D%E5%8A%A1%E4%BA%BA%E6%95%B0%E8%BF%87%E5%A4%9A%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%E3%80%82","autoReplyConfig.AGENT_OFFLINE_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E6%97%A0%E5%AE%A2%E6%9C%8D%E5%9C%A8%E7%BA%BF%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%E3%80%82","autoReplyConfig.UPDATE_LAST_SATISFY_KEYWORD":"XGMYD","autoReplyConfig.UPDATE_LAST_SATISFY_TIMEOUT_TIME":"30","autoReplyConfig.UPDATE_LAST_SATISFY_MSG":"","autoReplyConfig.SATISFY_USER_MSG_Y":"%E6%84%9F%E8%B0%A2%E6%82%A8%E7%9A%84%E8%AF%84%E4%BB%B7%2C%E7%A5%9D%E6%82%A8%E7%94%9F%E6%B4%BB%E6%84%89%E5%BF%AB~","autoReplyConfig.NOT_SATISFY_USER_MSG_Y":"%E6%82%A8%E7%9A%84%E8%AF%84%E4%BB%B7%E6%88%91%E4%BB%AC%E5%B7%B2%E6%94%B6%E5%88%B0%2C%E7%A5%9D%E6%82%A8%E7%94%9F%E6%B4%BB%E6%84%89%E5%BF%AB~","autoReplyConfig.SATISFY_TIME_OUT_TIME":"5","autoReplyConfig.SATISFY_TIME_OUT_MSG":"%E8%AF%84%E4%BB%B7%E5%B7%B2%E8%BF%87%E6%9C%9F","autoReplyConfig.SATISFY_USER_MSG_N":"%E6%82%A8%E7%9A%84%E8%AF%84%E4%BB%B7%E6%88%91%E4%BB%AC%E5%B7%B2%E6%94%B6%E5%88%B0%EF%BC%8C%E7%A5%9D%E6%82%A8%E7%94%9F%E6%B4%BB%E6%84%89%E5%BF%AB~","autoReplyConfig.UPDATE_SATISFY_SUCC_MSG":"%E6%84%9F%E8%B0%A2%E6%82%A8%E5%86%8D%E6%AC%A1%E8%AF%84%E4%BB%B7","autoReplyConfig.UPDATE_SATISFY_TIMEOUT_MSG":"%E8%AF%84%E4%BB%B7%E5%B7%B2%E8%BF%87%E6%9C%9F","autoReplyConfig.WORD_FORM_URL":"","autoReplyConfig.SYSTEM_NOTINSERVICE_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E4%B8%BA%E7%B3%BB%E7%BB%9F%E9%9D%9E%E5%B7%A5%E4%BD%9C%E6%97%B6%E9%97%B4%EF%BC%8C%E6%9C%8D%E5%8A%A1%E6%97%B6%E9%97%B4%E4%B8%BA%23serviceTime%23%EF%BC%8C%E6%82%A8%E5%8F%AF%E4%BB%A5%E5%86%99%E4%B8%8B%E7%95%99%E8%A8%80%EF%BC%8C%E6%88%91%E4%BB%AC%E4%BC%9A%E5%B0%BD%E5%BF%AB%E8%B7%9F%E8%BF%9B%E5%A4%84%E7%90%86%EF%BC%8C%E8%BE%93%E5%85%A588%E7%BB%93%E6%9D%9F%E7%95%99%E8%A8%80%E3%80%82","autoReplyConfig.H5_CKCODE_TIMEOUT":"10","autoReplyConfig.H5_CKCODE_FAIL_CONTENT":"","autoReplyConfig.CUST_SENSITIVE_REPLY":"%E6%82%A8%E8%BE%93%E5%85%A5%E7%9A%84%E4%BF%A1%E6%81%AF%E4%B8%AD%E5%8C%85%E5%90%AB%E6%95%8F%E6%84%9F%E8%AF%8D%3A%23sensitiveWord%23","autoReplyConfig.INVITATION_CODE_TIMEOUT":"30","autoReplyConfig.INVITE_SMS_MSG":"%E5%AE%A2%E6%9C%8D%23agentNickName%23%E9%82%80%E8%AF%B7%E4%BD%A0%E5%9C%A8%E7%BA%BF%E8%81%8A%E5%A4%A9%EF%BC%8C%E8%AF%B7%E7%82%B9%E5%87%BB%E9%93%BE%E6%8E%A5%7C%7Chttps%3A%2F%2Fip%3Aport%2Fyc-media%2Fpages%2Fweb-chat.jsp%3FchannelKey%3D%23channelKey%23%26message%3D%23CODE_MSG%23%7C%7C%EF%BC%8C%E6%9C%89%E6%95%88%E6%9C%9F%E4%B8%BA%EF%BC%9A%23time%23","autoReplyConfig.BLACKLIST_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E7%B3%BB%E7%BB%9F%E4%B8%8D%E6%96%B9%E4%BE%BF%E6%8E%A5%E5%85%A5%EF%BC%8C%E8%AF%B7%E7%A8%8D%E5%90%8E%E5%86%8D%E8%AF%95%20%E3%80%82","autoReplyConfig.WORD_TIME_OUT_TIME":"5","autoReplyConfig.WORD_TIME_OUT_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%BD%93%E5%89%8D%E7%95%99%E8%A8%80%E5%B7%B2%E8%B6%85%E6%97%B6%EF%BC%8C%E6%84%9F%E8%B0%A2%E4%BD%A0%E7%9A%84%E7%95%99%E8%A8%80%E3%80%82","autoReplyConfig.WORD_END_MSG":"%E6%84%9F%E8%B0%A2%E6%82%A8%E7%9A%84%E7%95%99%E8%A8%80%EF%BC%8C%E6%88%91%E4%BB%AC%E4%BC%9A%E5%B0%BD%E5%BF%AB%E8%B7%9F%E8%BF%9B%E5%A4%84%E7%90%86%EF%BC%81","autoReplyConfig.GOODBYE_MSG":"%E6%82%A8%E5%A5%BD%EF%BC%8C%E6%9C%AC%E6%AC%A1%E4%BC%9A%E8%AF%9D%E7%BB%93%E6%9D%9F%EF%BC%8C%E6%84%9F%E8%B0%A2%E6%82%A8%E4%BD%BF%E7%94%A8%E6%88%91%E4%BB%AC%E7%9A%84%E7%B3%BB%E7%BB%9F%EF%BC%8C%E5%86%8D%E8%A7%81%E3%80%82","autoReplyConfig.INTO_WORD_MSG":"%E6%82%A8%E5%8F%AF%E4%BB%A5%E5%86%99%E4%B8%8B%E7%95%99%E8%A8%80%EF%BC%8C%E6%88%91%E4%BB%AC%E4%BC%9A%E5%B0%BD%E5%BF%AB%E8%B7%9F%E8%BF%9B%E5%A4%84%E7%90%86%EF%BC%8C%E8%BE%93%E5%85%A5%E2%80%9C88%E2%80%9D%E7%BB%93%E6%9D%9F%E7%95%99%E8%A8%80","autoReplyConfig.NOT_KEYWORD_MSG":"%E6%9C%AA%E5%91%BD%E4%B8%AD%E5%85%B3%E9%94%AE%E5%AD%97%EF%BC%8C%E8%AF%B7%E5%9C%A8%E5%9B%9E%E5%A4%8D%E8%AF%AD%E4%B8%AD%E9%85%8D%E7%BD%AE%E2%80%9C%E6%9C%AA%E5%91%BD%E4%B8%AD%E5%85%B3%E9%94%AE%E5%AD%97%E7%9A%84%E6%8F%90%E7%A4%BA%E8%AF%AD%E2%80%9D%EF%BC%8C%E5%B9%B6%E6%B7%BB%E5%8A%A0%E7%9B%B8%E5%85%B3%E5%85%B3%E9%94%AE%E5%AD%97","autoReplyConfig.ROBOT_TRANSLATION_INFO_ID":"1","autoReplyConfig.ROBOT_TRANSLATION_SRC":"zh","autoReplyConfig.ROBOT_TRANSLATION_DEST":"zh","autoReplyConfig.FOLLOW_MSG":""}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 自动回复语保存成功！
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "delete_web_channel"
      describe: "删除网页渠道"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: delete
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "90"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-list.jsp
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"channelId":"82486663251817589121654","channelKey":"9874"}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 删除成功！
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "channel_set_robot"
      describe: "机器人配置"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: channelSetRobot
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "2069"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/robot/channel-robot-edit.jsp
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"ID":"82569416176189548044010","NAME":"智能客服机器人","ROBOT_TYPE":"yunqu3","ROBOT_IP":"************","ROBOT_PORT":"8080","ROBOT_APP_ID":"csr_1","ROBOT_APP_SECRET":"1","REQ_TYPE":"http","CREATE_TIME":"2025-03-27 14:53:02","CREATE_ACC":"8002@ekf","CREATE_NAME":"8002","CREATE_DEPT":"500","UPDATE_TIME":"2025-05-19 14:04:00","UPDATE_ACC":"8008@ekf","ENABLE_STATUS":"","SORT_NUM":"1","ENT_ID":"1000","BUSI_ORDER_ID":"83880897233159997654981","EX_JSON":"{\"ROBOT_SOURCE_ID\":\"\",\"FAQ_TYPE\":\"1\",\"RECEIVE_ENCODE\":\"GBK\",\"ROBOT_CHANNEL_ID\":\"0000\",\"SEND_ENCODE\":\"GBK\",\"KM_CHANNEL_KEY\":\"\",\"ROBOT_SYS_NUM\":\"\"}","TOKEN_INFO":"{\n\t\"robotAppId\":\"csr_1\",\n\t\"reqTimestamp\":1751335616693,\n\t\"channelKey\":\"1002\",\n\t\"robotIp\":\"************\",\n\t\"reqTime\":\"2025-07-01 10:06:56\",\n\t\"tokenMachine\":\"110-241;00-50-56-97-D0-19;**************;8344\",\n\t\"token\":\"0441E2AECA3617D0FE4C9019B936367F\"\n}","TOKEN":"0441E2AECA3617D0FE4C9019B936367F","CHANNELID":["82486620298687314487863"],"CHANNELKEY":[1111],"ALLOW_KEYWORDS":"","REFUSE_KEYWORDS":"","ALLOW_IN_TIMES_DAY":"100","NOT_SATISY_TIMES_TO_AGENT":"","BAD_REVIEW_REASON":"","ASK_ROBOT_TIMES_TO_AGENT":"0","SEQ_RULE":"1","ROBOT_EX_PARAM":"a=1&b=2","ROBOT_ANSWER_EVAL":"1","ROBOT_BOTTOM_LIST":"1"}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 操作成功!
          msg: assert response body msg
        - check: body.data
          assert: equals
          expect: 修改成功
          msg: assert response body data
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "updates"
      describe: "修改工作时间"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: updates
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "203"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-list.jsp
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"BEGIN_WORK_TIME1":"08:30","END_WORK_TIME1":"12:30","ids":["82486620298687314487863","82486635115547395327549"],"keys":[1111,7001]}'
          # data: '{"BEGIN_WORK_TIME2":"13:30","END_WORK_TIME2":"20:30","ids":["82486620298687314487863","82486635115547395327549"],"keys":[1111,7001]}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 修改成功！
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "batch_delete_by_ids"
      describe: "批量删除网页渠道"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: batchDeleteByIds
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "90"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-list.jsp
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"ids":["82486586567317079839677","82486586809637082349830"]}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 删除成功
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "get_channel_by_input"
      describe: "查询渠道"
      request:
        method: POST
        url: /cc-media/webcall
        # params:
          # action: batchDeleteByIds
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "347"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-list.jsp
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"params":{"channelName":"测试网页","channelKey":"1111","channelType":"1","channelState":"0","toggle":""},"controls":["channel.list","common.getDict(CC_BASE_CHANNEL_STATUS)","common.getDict(CC_BASE_CHANNEL_TYPE)"]}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.'channel.list'.type
          assert: equals
          expect: LIST
          msg: assert response body msg
        - check: body.'channel.list'.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "add_channel_key"
      describe: "添加按键配置"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: addChannelKey
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "583"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-key-list.html?channelId=82486620298687314487863&channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&entId=
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"channelKey.ENT_ID":"","channelKey.CHANNEL_ID":"82486620298687314487863","channelKey.KEY_ID":"","channelKey.CHANNEL_KEY":"1","channelKey.KEY_NAME":"转人工","channelKey.KEY_CODE":"1","channelKey.KEY_TYPE":"1","channelKey.KEY_CONTENT":"正在转人工","oldQueue":"","channelKey.QUEUE_ID":"11","channelKey.IS_SHOW":"1","channelKey.uploadImg":"1","channelKey.IMG_URL":""}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 保存成功！
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "update_channel_key"
      describe: "修改按键配置"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: updateChannelKey
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "644"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-key-list.html?channelId=82486620298687314487863&channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&entId=
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"channelKey.ENT_ID":"","channelKey.CHANNEL_ID":"82486620298687314487863","channelKey.KEY_ID":"82485803028659311828429","channelKey.CHANNEL_KEY":"1","channelKey.KEY_NAME":"转人工","channelKey.KEY_CODE":"1","channelKey.KEY_TYPE":"1","channelKey.KEY_CONTENT":"正在转人工请稍后！","oldQueue":"11","channelKey.QUEUE_ID":"11","channelKey.IS_SHOW":"1","channelKey.uploadImg":"1","channelKey.IMG_URL":""}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 修改成功！
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "get_channel_keyList"
      describe: "查询按键配置"
      request:
        method: POST
        url: /cc-media/webcall
        params:
          action: channel.keyList
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "201"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-key-list.html?channelId=82486620298687314487863&channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&entId=
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"pageType":3,"pageIndex":1,"pageSize":12,"channelId":"82486620298687314487863","keyName":"转人工","keyType":"1"}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 请求成功!
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "delete_channel_key"
      describe: "删除按键配置"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: deleteKey
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "164"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-key-list.html?channelId=82486620298687314487863&channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&entId=
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"keyId":"82485803028659311828429","channelId":"82486620298687314487863","channelKey":"1111","QUEUE_ID":"11"}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 删除成功！
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "get_customer_url"
      describe: "客户端测试"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: getCustomerUrl
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "11"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-list.jsp
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 操作成功!
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "update_style_config"
      describe: "H5客户端配置"
      request:
        method: POST
        url: /cc-media/servlet/channel
        params:
          action: UpdateStyleConfig
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "4465"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-h5style-config.jsp?channelId=83876540198929948528169&channelName=%E5%9C%A8%E7%BA%BF%E5%AE%A2%E6%9C%8D&channelType=1&channelKey=1001
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
          data: '{"CHANNEL_ID":"83876540198929948528169","CHANNEL_KEY":"1001","CHANNEL_NAME":"在线客服","styleConfig.H5_LOGO_URL":"/yc-media/getFile?id=b42f0693aa2c40429bc164fb886427b9&authType=01","styleConfig.H5_LOGO_URL2":"","styleConfig.H5_BACKGROUND_URL":"","styleConfig.H5_ROBOT_ICON":"","styleConfig.H5_SYSTEM_ICON":"","styleConfig.H5_AGENT_ICON":"","styleConfig.H5_CUSTOMER_ICON":"","styleConfig.H5_INFO_GATHER_LOGO":"","styleConfig.H5_ROBOT_NAME":"测试","styleConfig.H5_CHANNEL_NICK_NAME":"测试","styleConfig.H5_TITLE":"测试","styleConfig.H5_SUB_TITLE":"","styleConfig.H5_HEAD_COLOR":"#00ff11","styleConfig.H5_HEAD_FONT_COLOR":"","styleConfig.H5_FONT_STYLE":"","styleConfig.H5_SHOW_NICKNAME":"","styleConfig.H5_AGENT_FONT_STYLE":"","styleConfig.H5_CUSTOMER_FONT_STYLE":"","styleConfig.H5_AGENT_FONT_COLOR":"","styleConfig.H5_CUSTOMER_FONT_COLOR":"","styleConfig.H5_AGENT_FONT_BG_COLOR":"","styleConfig.H5_CUSTOMER_FONT_BG_COLOR":"","styleConfig.H5_ROBOT_FONT_STYLE":"","styleConfig.H5_SYSTEM_FONT_STYLE":"","styleConfig.H5_ROBOT_FONT_COLOR":"","styleConfig.H5_SYSTEM_FONT_COLOR":"","styleConfig.H5_ROBOT_FONT_BG_COLOR":"","styleConfig.H5_SYSTEM_FONT_BG_COLOR":"","styleConfig.H5_SKIN_CODE":"","styleConfig.H5_SYSTEM_NAME":"","styleConfig.CUSTOM_CONFIG_ITEM":"","styleConfig.CHANNEL_NAV_TYPE":"2","commonConfig.NAVIGATION_TYPE":"1","styleConfig.SIGN_SWITCH":"0","styleConfig.SIGN_SECRET":"","styleConfig.SIGN_EFFECT":"","styleConfig.VIDEO_SWITCH":"0","styleConfig.VIDEO_VIP_SWITCH":"0","styleConfig.BTN_AUTH_AUDIO":"1","styleConfig.BTN_AUTH_VIDEO":"1","styleConfig.BTN_AUTH_PIC":"1","styleConfig.BTN_AUTH_FILE":"1","styleConfig.BTN_AUTH_TIPS":"需要使用您设备的#btnName#权限;needs your device #btnName#auth","styleConfig.BTN_AUTH_SEARCH":"0","styleConfig.BTN_AUTH_DEBUG":"0","styleConfig.SHOW_END_NAV":"1","commonConfig.SHOW_HIS_MSG":"1","commonConfig.SHOW_HIS_MSG_RANGE":"0","commonConfig.OPEN_HOT_PIC":"1","commonConfig.CURR_CHANNLE_HIS_MSG":"0","commonConfig.OPEN_HOT_QUESTION":"1","commonConfig.HOT_QUESTION_SOURCE":"1","styleConfig.REALTIME_VOICE_FLAG":"1","styleConfig.REALTIME_VOICE_TYPE":"wav","styleConfig.VOICE_TOWORD_URL":"http://**************:8088/cc-asr/interface/recognize2?id=82533943160407566280717","styleConfig.ASR_CONFIG":"","styleConfig.REALTIME_VOICE_SECOND":"10","styleConfig.BUSI_SCRIPT_URL":"","styleConfig.ENABLE_CUST_LEAVE_MSG":"1","styleConfig.ROBOT_TO_AGENT_CONFIRM":"0","styleConfig.ROBOT_TO_AGENT_CONFIRM_MSG1":"当前咨询人工客服人数过多，确认是否转人工？","styleConfig.ROBOT_TO_AGENT_CONFIRM_MSG2":"当前为非工作时间，确认是否转人工？","styleConfig.H5_INFO_GATHER_FLAG":"0","styleConfig.H5_INFO_GATHER_URL":"","styleConfig.H5_INFO_TYPE":"","styleConfig.TEL_NUM":"","styleConfig.showWelcome":"1","styleConfig.CHAT_INPUT_TIPS":"请用一句话简要描述问题","styleConfig.EXPECT_QUESTION_SOURCE":"1","styleConfig.TOP_NAV_QUESTION_SOURCE":"1","styleConfig.SHOOT_VIDEO":"0","styleConfig.WITHDRAM_MSG":"0","styleConfig.AUTO_INVITE_WEBSITE_USER":"0","styleConfig.AUTO_INVITE_WEBSITE_USER_SECONDS":"30"}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 操作成功!
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
    - name: "update_channel_satisfy"
      describe: "修改渠道满意度指标"
      request:
        method: POST
        url: /cc-media/servlet/satisf
        params:
          action: updateChannelSatisf
        headers:
          Accept: application/json, text/javascript, */*; q=0.01
          Accept-Encoding: gzip, deflate
          Accept-Language: zh-CN,zh;q=0.9
          Connection: keep-alive
          Content-Length: "3014"
          Content-Type: application/x-www-form-urlencoded; charset=UTF-8
          Host: **************:9060
          Origin: http://**************:9060
          Referer: http://**************:9060/cc-media/pages/media/channel-satisfy-edit.html?channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&channelId=82486620298687314487863
          User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
          X-Requested-With: XMLHttpRequest
          token: bWFyc0AyMDE5
        cookies:
          HMACCOUNT: 028EBC18F491ADCC
          Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
          Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
          JSESSIONID: A8940328DBEAD322317476C4BA4A7854
          JSESSIONID_NODE: 626F1344B5C370F48BD954CE916EAF65
          passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
#          data: '%7B%22manual%22%3A%7B%22satisfyList%22%3A%5B%7B%22NAME%22%3A%22%E9%9D%9E%E5%B8%B8%E6%BB%A1%E6%84%8F%22%2C%22CODE%22%3A%221%22%2C%22ITEM_TYPE%22%3A%221%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%80%81%E5%BA%A6%22%2C%22CODE%22%3A%221%22%2C%22SORT_NUM%22%3A%221%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%2C%7B%22NAME%22%3A%22%E6%BB%A1%E6%84%8F%22%2C%22CODE%22%3A%222%22%2C%22ITEM_TYPE%22%3A%221%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E4%B8%93%E4%B8%9A%E6%B0%B4%E5%B9%B3%22%2C%22CODE%22%3A%222%22%2C%22SORT_NUM%22%3A%222%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%2C%7B%22NAME%22%3A%22%E4%B8%80%E8%88%AC%22%2C%22CODE%22%3A%223%22%2C%22ITEM_TYPE%22%3A%222%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E6%B2%9F%E9%80%9A%E8%A1%A8%E8%BE%BE%22%2C%22CODE%22%3A%223%22%2C%22SORT_NUM%22%3A%223%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%2C%7B%22NAME%22%3A%22%E5%AF%B9%E7%BB%93%E6%9E%9C%E4%B8%8D%E6%BB%A1%E6%84%8F%22%2C%22CODE%22%3A%224%22%2C%22ITEM_TYPE%22%3A%223%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E5%93%8D%E5%BA%94%E9%80%9F%E5%BA%A6%22%2C%22CODE%22%3A%224%22%2C%22SORT_NUM%22%3A%224%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%2C%7B%22NAME%22%3A%22%E5%AF%B9%E6%9C%8D%E5%8A%A1%E4%B8%8D%E6%BB%A1%E6%84%8F%22%2C%22CODE%22%3A%225%22%2C%22ITEM_TYPE%22%3A%223%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E5%93%8D%E5%BA%94%E9%80%9F%E5%BA%A6%22%2C%22CODE%22%3A%224%22%2C%22SORT_NUM%22%3A%224%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%5D%2C%22startWord%22%3A%22%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%AF%B9%E5%88%9A%E6%89%8D%E5%AE%A2%E6%9C%8D%E7%9A%84%E8%A7%A3%E7%AD%94%E8%BF%98%E6%BB%A1%E6%84%8F%E4%B9%88%EF%BC%9F%E9%BA%BB%E7%83%A6%E6%82%A8%E4%B8%BA%E5%AE%A2%E6%9C%8D%E5%9B%9E%E4%B8%AA%E8%AF%84%E4%BB%B7%E6%95%B0%E5%AD%97%E5%93%9F%EF%BC%8C%E6%82%A8%E7%9A%84%E5%9B%9E%E5%A4%8D%E9%9D%9E%E5%B8%B8%E9%87%8D%E8%A6%81%E5%93%A6%22%2C%22endWord%22%3A%22%E6%82%A8%E7%9A%84%E8%AF%84%E4%BB%B7%E6%98%AF%E6%88%91%E4%BB%AC%E8%BF%9B%E6%AD%A5%E7%9A%84%E5%8A%A8%E5%8A%9B%EF%BC%8C%E8%B0%A2%E8%B0%A2%E3%80%82%22%7D%2C%22robot%22%3A%7B%22satisfyList%22%3A%5B%5D%7D%2C%22third%22%3A%7B%22satisfyList%22%3A%5B%5D%7D%2C%22video%22%3A%7B%22satisfyList%22%3A%5B%5D%7D%2C%22channelId%22%3A%2282486620298687314487863%22%2C%22channelNo%22%3A%221111%22%2C%22channelName%22%3A%22%25E6%25B5%258B%25E8%25AF%2595%25E7%25BD%2591%25E9%25A1%25B5%22%7D'
          data: '{"manual":{"satisfyList":[{"NAME":"非常满意","CODE":"1","ITEM_TYPE":"1","detail":[{"NAME":"服务态度","CODE":"1","SORT_NUM":"1","ITEM2_TYPE":"1"}],"detail2":[{"NAME":"服务效率","CODE":"123we","SORT_NUM":"1"}]},{"NAME":"满意","CODE":"2","ITEM_TYPE":"1","detail":[{"NAME":"专业水平","CODE":"2","SORT_NUM":"2","ITEM2_TYPE":"1"}],"detail2":[{"NAME":"服务效率","CODE":"123we","SORT_NUM":"1"}]},{"NAME":"一般","CODE":"3","ITEM_TYPE":"2","detail":[{"NAME":"沟通表达","CODE":"3","SORT_NUM":"3","ITEM2_TYPE":"1"}],"detail2":[{"NAME":"服务效率","CODE":"123we","SORT_NUM":"1"}]},{"NAME":"对结果不满意","CODE":"4","ITEM_TYPE":"3","detail":[{"NAME":"响应速度","CODE":"4","SORT_NUM":"4","ITEM2_TYPE":"1"}],"detail2":[{"NAME":"服务效率","CODE":"123we","SORT_NUM":"1"}]},{"NAME":"对服务不满意","CODE":"5","ITEM_TYPE":"3","detail":[{"NAME":"响应速度","CODE":"4","SORT_NUM":"4","ITEM2_TYPE":"1"}],"detail2":[{"NAME":"服务效率","CODE":"123we","SORT_NUM":"1"}]}],"startWord":"您好，对刚才客服的解答还满意么？麻烦您为客服回个评价数字哟，您的回复非常重要哦","endWord":"您的评价是我们进步的动力，谢谢。"},"robot":{"satisfyList":[]},"third":{"satisfyList":[]},"video":{"satisfyList":[]},"channelId":"82486620298687314487863","channelNo":"1111","channelName":"%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5"}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 操作成功!
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
