config:
    name: testcase description
teststeps:
    - name: ""
      request:
        method: POST
        url: http://**************:9060/yc-login/login
        params:
            action: login
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "317"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-portal/login_yq.html?logoutType=1
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
            token: bWFyc0AyMDE5
        cookies:
            HMACCOUNT: 028EBC18F491ADCC
            Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
            JSESSIONID: A8940328DBEAD322317476C4BA4A7854
            passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
        body:
            data: '{"language":"zh-CN","j_username":"ODAwMkBla2Y=","j_password":"2R72jyzx63c8yTH54fCzxQ==","j_imagecode":"","callbackUrl":"","busiId":"007","loginType":"default","encryptType":0,"versionDate":"********"}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.busiId
          assert: equals
          expect: "007"
          msg: assert response body busiId
        - check: body.busiOrderId
          assert: equals
          expect: "83880897233159997654981"
          msg: assert response body busiOrderId
        - check: body.loginTimes
          assert: equals
          expect: "790"
          msg: assert response body loginTimes
        - check: body.msg
          assert: equals
          expect: 操作成功!
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
        - check: body.userAccount
          assert: equals
          expect: 8002@ekf
          msg: assert response body userAccount
        - check: body.userEntId
          assert: equals
          expect: "1000"
          msg: assert response body userEntId
        - check: body.userEntName
          assert: equals
          expect: 企业呼叫中心
          msg: assert response body userEntName
        - check: body.userId
          assert: equals
          expect: "83880896957889997330198"
          msg: assert response body userId
    - name: ""
      request:
        method: GET
        url: http://**************:9060/yc-login/authen
        params:
            action: success
            busiId: "007"
            ctxPath: /cc-portal
        headers:
            Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Host: **************:9060
            Referer: http://**************:9060/cc-portal/login_yq.html?logoutType=1
            Upgrade-Insecure-Requests: "1"
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
        cookies:
            HMACCOUNT: 028EBC18F491ADCC
            Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280: "**********"
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: **********,**********,**********
            JSESSIONID: A8940328DBEAD322317476C4BA4A7854
            JSESSIONID_NODE: 71BE10D82DE37D43DE766841C7A2B2DA
            passport: Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D
      validate:
        - check: status_code
          assert: equals
          expect: 302
          msg: assert response status code
