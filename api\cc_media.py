from api.mars_api import MARSAPI
from utils.utils import Utils
import json


class CCMEDIA(MARSAPI):
    def __init__(self, session=None):
        '''
        初始化CCMEDIA
        :param session: 可选，传入已存在的session实例
        '''
        super().__init__(session=session)
        self.channel = Utils.read_yaml('../data/cc_media_test.yaml')

    def add_web_channel(self, CHANNEL_NAME: str, CHANNEL_KEY: str, BEGIN_WORK_TIME1: str, END_WORK_TIME1: str,
                        WEB_URL: str) -> dict:
        '''
        新增网页渠道接口
        :param : CHANNEL_NAME: 渠道名称
        :param : CHANNEL_KEY: 渠道channelKey
        :param : BEGIN_WORK_TIME1: 工作时间1 开始时间
        :param : END_WORK_TIME1: 工作时间1 结束时间
        :param : WEB_URL: 服务器地址
        :return: 接口响应结果
        '''
        # 获取请求参数
        req = Utils.get_steps(self.channel, 'add_web_channel')

        # 替换请求参数
        param_dict = {
            'channel.CHANNEL_NAME': CHANNEL_NAME,
            'channel.CHANNEL_KEY': CHANNEL_KEY,
            'channel.commonConfig.BEGIN_WORK_TIME1': BEGIN_WORK_TIME1,
            'channel.commonConfig.END_WORK_TIME1': END_WORK_TIME1,
            'channel.commonConfig.WEB_URL': WEB_URL,
        }
        req['data']['data'] = Utils.key_replace(req['data']['data'], param_dict)

        # 发送请求
        response = self.send_request(**req)
        return response.json()

    def add_web_autoConfig(self, **kwargs):
        req = Utils.get_steps(self.channel, 'add_web_autoConfig')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def get_channelList(self, **kwargs):
        req = Utils.get_steps(self.channel, 'get_channelList')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def delete_web_channel(self, **kwargs):
        req = Utils.get_steps(self.channel, 'delete_web_channel')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def channel_set_robot(self, **kwargs):
        req = Utils.get_steps(self.channel, 'channel_set_robot')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def updates(self, **kwargs):
        req = Utils.get_steps(self.channel, 'updates')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def batch_delete_by_ids(self, **kwargs):
        req = Utils.get_steps(self.channel, 'batch_delete_by_ids')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def get_channel_by_input(self, **kwargs):
        req = Utils.get_steps(self.channel, 'get_channel_by_input')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def add_channel_key(self, **kwargs):
        req = Utils.get_steps(self.channel, 'add_channel_key')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def update_channel_key(self, **kwargs):
        req = Utils.get_steps(self.channel, 'update_channel_key')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def get_channel_keyList(self, **kwargs):
        req = Utils.get_steps(self.channel, 'get_channel_keyList')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def delete_channel_key(self, **kwargs):
        req = Utils.get_steps(self.channel, 'delete_channel_key')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def get_customer_url(self, **kwargs):
        req = Utils.get_steps(self.channel, 'get_customer_url')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def update_style_config(self, **kwargs):
        req = Utils.get_steps(self.channel, 'update_style_config')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()

    def update_channel_satisfy(self, **kwargs):
        req = Utils.get_steps(self.channel, 'update_channel_satisfy')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()
