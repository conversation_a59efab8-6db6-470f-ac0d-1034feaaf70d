import base64
import csv
import json
import os
import random
import string
from typing import Union, List, Dict, Any
from urllib import parse
import pystache
from openpyxl import load_workbook
import yaml
from utils.logger import logger


class Utils:

    @classmethod
    def read_yaml(cls, yaml_path: str) -> Dict:
        """读取yaml文件"""
        global abs_path
        try:
            abs_path = cls.get_data_path(os.path.join(yaml_path))
            if not os.path.exists(abs_path):
                raise FileNotFoundError(f"文件不存在: {abs_path}")
            with open(abs_path, 'r', encoding='utf-8') as f:
                return yaml.load(f.read(), Loader=yaml.FullLoader)
        except Exception as e:
            logger.error(f"读取yaml文件失败: {abs_path}, 错误: {str(e)}")
            raise

    @classmethod
    def write_yaml(cls, yaml_path: str, data: Dict) -> None:
        """写入yaml文件"""
        global abs_path
        try:
            abs_path = cls.get_data_path(yaml_path)
            with open(abs_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, allow_unicode=True, default_flow_style=False)
            logger.info(f"成功写入yaml文件: {abs_path}")
        except Exception as e:
            logger.error(f"写入yaml文件失败: {abs_path}, 错误: {str(e)}")
            raise

    @classmethod
    def get_json_data(cls, json_path: str) -> str:
        """读取json文件"""
        global abs_path
        try:
            abs_path = cls.get_data_path(json_path)
            if not os.path.exists(abs_path):
                raise FileNotFoundError(f"文件不存在: {abs_path}")
            with open(abs_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取json文件失败: {abs_path}, 错误: {str(e)}")
            raise

    @classmethod
    def get_excel_data(cls, excel_path: str, sheet_name: str = None) -> Union[List[List], Dict[str, List[List]]]:
        """
        读取excel文件
        Args:
            excel_path: excel文件路径
            sheet_name: 工作表名称，默认为None表示读取所有工作表
        Returns:
            如果指定sheet_name，返回该表的数据列表
            如果未指定sheet_name，返回包含所有表数据的字典
        """
        global abs_path
        try:
            abs_path = cls.get_data_path(excel_path)
            if not os.path.exists(abs_path):
                raise FileNotFoundError(f"文件不存在: {abs_path}")

            workbook = load_workbook(abs_path, data_only=True)

            if sheet_name:
                if sheet_name not in workbook.sheetnames:
                    raise ValueError(f"工作表 {sheet_name} 不存在")
                worksheet = workbook[sheet_name]
                data = [[cell.value for cell in row] for row in worksheet.rows]
                return data

            # 读取所有工作表
            result = {}
            for sheet in workbook.sheetnames:
                worksheet = workbook[sheet]
                result[sheet] = [[cell.value for cell in row] for row in worksheet.rows]

            logger.info(f"成功读取Excel文件: {abs_path}")
            return result

        except Exception as e:
            logger.error(f"读取excel文件失败: {abs_path}, 错误: {str(e)}")
            raise

    @classmethod
    def get_csv_data(cls, csv_path: str) -> List[List[str]]:
        """读取csv文件"""
        global abs_path
        try:
            abs_path = cls.get_data_path(csv_path)
            if not os.path.exists(abs_path):
                raise FileNotFoundError(f"文件不存在: {abs_path}")
            with open(abs_path, 'r', encoding='utf-8') as f:
                return list(csv.reader(f))
        except Exception as e:
            logger.error(f"读取csv文件失败: {abs_path}, 错误: {str(e)}")
            raise

    @classmethod
    def get_txt_data(cls, txt_path: str) -> str:
        """读取txt文件"""
        global abs_path
        try:
            abs_path = cls.get_data_path(txt_path)
            if not os.path.exists(abs_path):
                raise FileNotFoundError(f"文件不存在: {abs_path}")
            with open(abs_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取txt文件失败: {abs_path}, 错误: {str(e)}")
            raise

    @classmethod
    def get_data_path(cls, path):
        """
        获取文件的绝对路径
        :param path: 文件路径，可以是绝对路径或相对路径
        :return: 文件的绝对路径
        """
        # 如果是绝对路径，直接返回
        if os.path.isabs(path):
            return path

        # 获取项目根目录（common目录的上级目录）
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # 如果路径以 ../ 开头，从common目录开始解析
        if path.startswith('..'):
            return os.path.normpath(os.path.join(os.path.dirname(__file__), path))

        # 否则从项目根目录开始解析
        return os.path.join(root_dir, path)

    @classmethod
    def parse_quote(cls, payload) -> str:
        """请求体转为URL编码格式"""
        # json.dumps: 将python对象编码成Json字符串
        return parse.quote(json.dumps(payload))

    @classmethod
    def get_bs64(cls, data):
        """Base64编码"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            return base64.b64encode(data).decode('utf-8')
        except Exception as e:
            logger.error(f"Base64编码失败: {str(e)}")
            raise

    @classmethod
    def get_phone(cls):
        """生成随机手机号"""
        prefix = [
            '130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
            '145', '147', '149', '150', '151', '152', '153', '155', '156', '157',
            '158', '159', '165', '171', '172', '173', '174', '175', '176', '177',
            '178', '180', '181', '182', '183', '184', '185', '186', '187', '188',
            '189', '191'
        ]
        try:
            prefix_num = prefix[random.randint(0, len(prefix) - 1)]
            suffix = ''.join(random.sample(string.digits, 8))
            phone = f"{prefix_num}{suffix}"
            logger.info(f"生成随机手机号: {phone}")
            return phone
        except Exception as e:
            logger.error(f"生成随机手机号失败: {str(e)}")
            raise

    @classmethod
    def get_mustache(cls, template: str, data: Dict) -> str:
        """
        Mustache模板渲染
        将复杂的xml或者json请求体保存到文件模板(如json文件)中，使用{{name}}替换要修改的参数
        """
        try:
            return pystache.render(template, data)
        except Exception as e:
            logger.error(f"Mustache模板渲染失败: {str(e)}")
            raise

    @classmethod
    def json_dumps(cls, data):
        """
		json.dumps将一个Python数据结构转换为JSON
		:param data: reponse返回的json结构数据
		:return:
		"""
        return json.dumps(data, indent=2, ensure_ascii=False)

    @classmethod
    def extract_json(cls, obj: Union[Dict, List], path: List[str]) -> List:
        """
        递归提取JSON数据中的指定字段
        Args:
            obj: JSON对象或数组
            path: 要提取的字段路径列表，如 ["data", "items", "id"]
        Returns:
            提取到的所有值列表
        """

        def extract(data: Any, keys: List[str], index: int = 0, result: List = None) -> List:
            if result is None:
                result = []

            # 处理空值
            if data is None:
                result.append(None)
                return result

            current_key = keys[index]

            # 到达路径最后一个键
            if index == len(keys) - 1:
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            result.append(item.get(current_key))
                elif isinstance(data, dict):
                    result.append(data.get(current_key))
                return result

            # 继续递归
            if isinstance(data, dict):
                if current_key in data:
                    extract(data[current_key], keys, index + 1, result)
                else:
                    result.append(None)
            elif isinstance(data, list):
                for item in data:
                    extract(item, keys, index, result)

            return result

        try:
            return extract(obj, path)
        except Exception as e:
            logger.error(f"JSON数据提取失败: {str(e)}")
            raise

    @classmethod
    def key_replace(cls, data: Union[str, dict], mappings: Dict[str, Any]) -> str:
        """
        替换JSON字符串中的值，支持传入str或dict
        Args:
            data: JSON格式的字符串或字典
            mappings: 需要替换的键值映射，支持嵌套格式
        Returns:
            替换后的JSON字符串
        """
        try:
            # 如果是dict，直接用，否则转为dict
            if isinstance(data, dict):
                replaced_dict = data
            else:
                replaced_dict = json.loads(data)

            # 遍历映射进行替换
            for key, value in mappings.items():
                if ":" in key:
                    parent_key, child_key = key.split(':')
                    if parent_key in replaced_dict:
                        replaced_dict[parent_key][child_key] = value
                else:
                    replaced_dict[key] = value

            return json.dumps(replaced_dict, ensure_ascii=False)

        except Exception as e:
            logger.error(f"替换JSON字符串失败: {str(e)}")
            raise

    @classmethod
    def get_steps(cls, data: dict, stepsname):
        try:
            # 获取环境配置
            env_config = cls.read_yaml("../data/env.yml")
            # 从配置文件获取当前环境，如果未配置则抛出异常
            current_env = env_config['current_env']
            base_url = env_config['env'][current_env]['host']
            logger.info(f"当前环境: {current_env}, base_url: {base_url}")

            # 获取request的值
            for i in data.get("teststeps"):
                if i["name"] == stepsname:
                    request = i.get("request", {})
                    url = request.get("url")
                    # 如果是相对路径，添加base_url
                    if url and url.startswith('/'):
                        url = base_url + url

                    # 兼容 body.data 结构
                    req_data = request.get("data")
                    if not req_data and "body" in request and request["body"] and "data" in request["body"]:
                        req_data = request["body"]["data"]

                    # 如果 req_data 是字符串，自动转成 dict
                    if isinstance(req_data, str):
                        try:
                            req_data = json.loads(req_data)
                        except Exception:
                            pass

                    # 保证 req_data 结构为 {'data': ...}
                    if isinstance(req_data, dict) and 'data' not in req_data:
                        req_data = {'data': req_data}

                    req = {
                        "url": url,
                        "method": request.get("method"),
                        "params": request.get("params"),
                        "headers": request.get("headers"),
                        "data": req_data,
                        "json": request.get("json"),
                        "timeout": 5
                    }
                    # 移除None值的键
                    return {k: v for k, v in req.items() if v is not None}
        except Exception as e:
            logger.error(f"读取request报错: {str(e)}")
            raise

    @classmethod
    def generate_unique_name(cls, prefix: str) -> str:
        """
        生成唯一的名称
        Args:
            prefix: 名称前缀，默认为"质检规则"
        Returns:
            str: 唯一的名称，格式为 prefix_时间戳_随机字符串
        """
        try:
            from datetime import datetime
            # 使用时间戳（精确到微秒）+ 随机字符串确保并发时不重复
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")
            random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=4))
            unique_name = f"{prefix}_{timestamp}_{random_str}"
            logger.info(f"生成唯一名称: {unique_name}")
            return unique_name
        except Exception as e:
            logger.error(f"生成唯一名称失败: {str(e)}")
            raise