[2025-07-04 16:40:18] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://172.16.110.241:9060
[2025-07-04 16:40:18] [base_api.py:40] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://172.16.110.241:9060/yc-login/login
- 请求头: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'login'}
- 请求体: {'data': '{"j_username": "ODAwMkBla2Y=", "j_password": "2R72jyzx63c8yTH54fCzxQ==", "j_imagecode": "", "callbackUrl": "", "busiId": "007", "loginType": "default", "versionDate": "20220920"}'}
- JSON数据: None
[2025-07-04 16:40:18] [base_api.py:34] [base_api:send_request] [ERROR]- 请求失败: 'str' object has no attribute 'values'
