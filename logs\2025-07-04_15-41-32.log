[2025-07-04 15:41:32] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-04 15:41:32] [base_api.py:39] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/yc-login/login
- 请求头: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'login'}
- 请求体: {'data': '{"j_username": "ODAwMkBla2Y=", "j_password": "2R72jyzx63c8yTH54fCzxQ==", "j_imagecode": "", "callbackUrl": "", "busiId": "007", "loginType": "default", "versionDate": "20220920"}'}
- JSON数据: None
[2025-07-04 15:41:32] [base_api.py:83] [base_api:_handle_form_data] [INFO]- URL编码后的请求体: data=%7B%22j_username%22%3A%20%22ODAwMkBla2Y%3D%22%2C%20%22j_password%22%3A%20%222R72jyzx63c8yTH54fCzxQ%3D%3D%22%2C%20%22j_imagecode%22%3A%20%22%22%2C%20%22callbackUrl%22%3A%20%22%22%2C%20%22busiId%22%3A%20%22007%22%2C%20%22loginType%22%3A%20%22default%22%2C%20%22versionDate%22%3A%20%2220220920%22%7D
[2025-07-04 15:41:32] [base_api.py:51] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'x-frame-options': 'SAMEORIGIN, SAMEORIGIN', 'X-Content-Type-Options': 'nosniff, nosniff', 'X-XSS-Protection': '1; mode=block, 1; mode=block', 'Content-Security-Policy': 'none', 'Access-Control-Allow-Methods': 'GET,POST', 'Set-Cookie': 'JSESSIONID=CB295B4FAA36C5AB891D109C496FE72D; Path=/yc-login; HttpOnly, JSESSIONID_NODE=E99BDB39BFF282890120F21D4D51E1FD; Path=/; HttpOnly, mars_token=MAyxGLIO5nBMwEr38SD8P2Z8c6O1H6j+5fC+svxkk3clslBVC/w/70M4/tfQQ8CGidnB0cla00tww37bEBBujMBE3cPa5VHyTnDZgkRFpDw=; Max-Age=43200; Expires=Fri, 04 Jul 2025 19:41:32 GMT; Path=/; Secure; HttpOnly', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Fri, 04 Jul 2025 07:41:32 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: {"msg":"操作成功!","userEntId":"1000","data":[{"BUSI_ORDER_ID":"82546991020289016354594","BUSI_ID":"002","BUSI_NAME":"云电销(基础版)","APP_CONTEXT_PATH":"/yc-portal","BUSI_LOGO_URL":"slt-ydx.png","VALID_DATE":"2025-04-22","INVALID_DATE":"2031-04-22"},{"BUSI_ORDER_ID":"83880897233159997654981","BUSI_ID":"007","BUSI_NAME":"企业呼叫中心","APP_CONTEXT_PATH":"/cc-portal","BUSI_LOGO_URL":"slt-ykf.png","VALID_DATE":"2021-01-29","INVALID_DATE":"2025-01-30"},{"BUSI_ORDER_ID":"82575451131639974023157","BUSI_ID":"015","BUSI_NAME":"智能知识库","APP_CONTEXT_PATH":"/yc-alknowledge","BUSI_LOGO_URL":"slt-ykf.png","VALID_DATE":"2025-03-20","INVALID_DATE":"2031-03-20"},{"BUSI_ORDER_ID":"82552278394919165788429","BUSI_ID":"016","BUSI_NAME":"语音机器人","APP_CONTEXT_PATH":"/mk-robotvue","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-04-16","INVALID_DATE":"2031-04-16"},{"BUSI_ORDER_ID":"82568438201939908172984","BUSI_ID":"018","BUSI_NAME":"智能分析平台","APP_CONTEXT_PATH":"/yc-aianalysis","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-03-28","INVALID_DATE":"2030-03-28"},{"BUSI_ORDER_ID":"82552095494469152320151","BUSI_ID":"201","BUSI_NAME":"智能外呼","APP_CONTEXT_PATH":"/yc-call","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-04-16","INVALID_DATE":"2031-04-16"}],"busiId":"007","userAccount":"8002@ekf","loginTimes":"905","userEntName":"企业呼叫中心","state":1,"busiOrderId":"83880897233159997654981","userId":"83880896957889997330198"}
[2025-07-04 15:41:32] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-04 15:41:32] [base_api.py:39] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/cc-media/servlet/channel
- 请求头: {'Accept': 'application/json, text/javascript, */*; q=0.01', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9', 'Connection': 'keep-alive', 'Content-Length': '4465', 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'Host': '**************:9060', 'Origin': 'http://**************:9060', 'Referer': 'http://**************:9060/cc-media/pages/media/channel-h5style-config.jsp?channelId=83876540198929948528169&channelName=%E5%9C%A8%E7%BA%BF%E5%AE%A2%E6%9C%8D&channelType=1&channelKey=1001', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'UpdateStyleConfig'}
- 请求体: {'data': {'CHANNEL_ID': '83876540198929948528169', 'CHANNEL_KEY': '1001', 'CHANNEL_NAME': '在线客服', 'styleConfig.H5_LOGO_URL': '/yc-media/getFile?id=b42f0693aa2c40429bc164fb886427b9&authType=01', 'styleConfig.H5_LOGO_URL2': '', 'styleConfig.H5_BACKGROUND_URL': '', 'styleConfig.H5_ROBOT_ICON': '', 'styleConfig.H5_SYSTEM_ICON': '', 'styleConfig.H5_AGENT_ICON': '', 'styleConfig.H5_CUSTOMER_ICON': '', 'styleConfig.H5_INFO_GATHER_LOGO': '', 'styleConfig.H5_ROBOT_NAME': '测试', 'styleConfig.H5_CHANNEL_NICK_NAME': '测试', 'styleConfig.H5_TITLE': '测试', 'styleConfig.H5_SUB_TITLE': '', 'styleConfig.H5_HEAD_COLOR': '#00ff11', 'styleConfig.H5_HEAD_FONT_COLOR': '', 'styleConfig.H5_FONT_STYLE': '', 'styleConfig.H5_SHOW_NICKNAME': '', 'styleConfig.H5_AGENT_FONT_STYLE': '', 'styleConfig.H5_CUSTOMER_FONT_STYLE': '', 'styleConfig.H5_AGENT_FONT_COLOR': '', 'styleConfig.H5_CUSTOMER_FONT_COLOR': '', 'styleConfig.H5_AGENT_FONT_BG_COLOR': '', 'styleConfig.H5_CUSTOMER_FONT_BG_COLOR': '', 'styleConfig.H5_ROBOT_FONT_STYLE': '', 'styleConfig.H5_SYSTEM_FONT_STYLE': '', 'styleConfig.H5_ROBOT_FONT_COLOR': '', 'styleConfig.H5_SYSTEM_FONT_COLOR': '', 'styleConfig.H5_ROBOT_FONT_BG_COLOR': '', 'styleConfig.H5_SYSTEM_FONT_BG_COLOR': '', 'styleConfig.H5_SKIN_CODE': '', 'styleConfig.H5_SYSTEM_NAME': '', 'styleConfig.CUSTOM_CONFIG_ITEM': '', 'styleConfig.CHANNEL_NAV_TYPE': '2', 'commonConfig.NAVIGATION_TYPE': '1', 'styleConfig.SIGN_SWITCH': '0', 'styleConfig.SIGN_SECRET': '', 'styleConfig.SIGN_EFFECT': '', 'styleConfig.VIDEO_SWITCH': '0', 'styleConfig.VIDEO_VIP_SWITCH': '0', 'styleConfig.BTN_AUTH_AUDIO': '1', 'styleConfig.BTN_AUTH_VIDEO': '1', 'styleConfig.BTN_AUTH_PIC': '1', 'styleConfig.BTN_AUTH_FILE': '1', 'styleConfig.BTN_AUTH_TIPS': '需要使用您设备的#btnName#权限;needs your device #btnName#auth', 'styleConfig.BTN_AUTH_SEARCH': '0', 'styleConfig.BTN_AUTH_DEBUG': '0', 'styleConfig.SHOW_END_NAV': '1', 'commonConfig.SHOW_HIS_MSG': '1', 'commonConfig.SHOW_HIS_MSG_RANGE': '0', 'commonConfig.OPEN_HOT_PIC': '1', 'commonConfig.CURR_CHANNLE_HIS_MSG': '0', 'commonConfig.OPEN_HOT_QUESTION': '1', 'commonConfig.HOT_QUESTION_SOURCE': '1', 'styleConfig.REALTIME_VOICE_FLAG': '1', 'styleConfig.REALTIME_VOICE_TYPE': 'wav', 'styleConfig.VOICE_TOWORD_URL': 'http://**************:8088/cc-asr/interface/recognize2?id=82533943160407566280717', 'styleConfig.ASR_CONFIG': '', 'styleConfig.REALTIME_VOICE_SECOND': '10', 'styleConfig.BUSI_SCRIPT_URL': '', 'styleConfig.ENABLE_CUST_LEAVE_MSG': '1', 'styleConfig.ROBOT_TO_AGENT_CONFIRM': '0', 'styleConfig.ROBOT_TO_AGENT_CONFIRM_MSG1': '当前咨询人工客服人数过多，确认是否转人工？', 'styleConfig.ROBOT_TO_AGENT_CONFIRM_MSG2': '当前为非工作时间，确认是否转人工？', 'styleConfig.H5_INFO_GATHER_FLAG': '0', 'styleConfig.H5_INFO_GATHER_URL': '', 'styleConfig.H5_INFO_TYPE': '', 'styleConfig.TEL_NUM': '', 'styleConfig.showWelcome': '1', 'styleConfig.CHAT_INPUT_TIPS': '请用一句话简要描述问题', 'styleConfig.EXPECT_QUESTION_SOURCE': '1', 'styleConfig.TOP_NAV_QUESTION_SOURCE': '1', 'styleConfig.SHOOT_VIDEO': '0', 'styleConfig.WITHDRAM_MSG': '0', 'styleConfig.AUTO_INVITE_WEBSITE_USER': '0', 'styleConfig.AUTO_INVITE_WEBSITE_USER_SECONDS': '30'}}
- JSON数据: None
[2025-07-04 15:41:32] [base_api.py:83] [base_api:_handle_form_data] [INFO]- URL编码后的请求体: data=%7B%27CHANNEL_ID%27%3A%20%2783876540198929948528169%27%2C%20%27CHANNEL_KEY%27%3A%20%271001%27%2C%20%27CHANNEL_NAME%27%3A%20%27%E5%9C%A8%E7%BA%BF%E5%AE%A2%E6%9C%8D%27%2C%20%27styleConfig.H5_LOGO_URL%27%3A%20%27/yc-media/getFile%3Fid%3Db42f0693aa2c40429bc164fb886427b9%26authType%3D01%27%2C%20%27styleConfig.H5_LOGO_URL2%27%3A%20%27%27%2C%20%27styleConfig.H5_BACKGROUND_URL%27%3A%20%27%27%2C%20%27styleConfig.H5_ROBOT_ICON%27%3A%20%27%27%2C%20%27styleConfig.H5_SYSTEM_ICON%27%3A%20%27%27%2C%20%27styleConfig.H5_AGENT_ICON%27%3A%20%27%27%2C%20%27styleConfig.H5_CUSTOMER_ICON%27%3A%20%27%27%2C%20%27styleConfig.H5_INFO_GATHER_LOGO%27%3A%20%27%27%2C%20%27styleConfig.H5_ROBOT_NAME%27%3A%20%27%E6%B5%8B%E8%AF%95%27%2C%20%27styleConfig.H5_CHANNEL_NICK_NAME%27%3A%20%27%E6%B5%8B%E8%AF%95%27%2C%20%27styleConfig.H5_TITLE%27%3A%20%27%E6%B5%8B%E8%AF%95%27%2C%20%27styleConfig.H5_SUB_TITLE%27%3A%20%27%27%2C%20%27styleConfig.H5_HEAD_COLOR%27%3A%20%27%2300ff11%27%2C%20%27styleConfig.H5_HEAD_FONT_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_FONT_STYLE%27%3A%20%27%27%2C%20%27styleConfig.H5_SHOW_NICKNAME%27%3A%20%27%27%2C%20%27styleConfig.H5_AGENT_FONT_STYLE%27%3A%20%27%27%2C%20%27styleConfig.H5_CUSTOMER_FONT_STYLE%27%3A%20%27%27%2C%20%27styleConfig.H5_AGENT_FONT_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_CUSTOMER_FONT_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_AGENT_FONT_BG_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_CUSTOMER_FONT_BG_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_ROBOT_FONT_STYLE%27%3A%20%27%27%2C%20%27styleConfig.H5_SYSTEM_FONT_STYLE%27%3A%20%27%27%2C%20%27styleConfig.H5_ROBOT_FONT_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_SYSTEM_FONT_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_ROBOT_FONT_BG_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_SYSTEM_FONT_BG_COLOR%27%3A%20%27%27%2C%20%27styleConfig.H5_SKIN_CODE%27%3A%20%27%27%2C%20%27styleConfig.H5_SYSTEM_NAME%27%3A%20%27%27%2C%20%27styleConfig.CUSTOM_CONFIG_ITEM%27%3A%20%27%27%2C%20%27styleConfig.CHANNEL_NAV_TYPE%27%3A%20%272%27%2C%20%27commonConfig.NAVIGATION_TYPE%27%3A%20%271%27%2C%20%27styleConfig.SIGN_SWITCH%27%3A%20%270%27%2C%20%27styleConfig.SIGN_SECRET%27%3A%20%27%27%2C%20%27styleConfig.SIGN_EFFECT%27%3A%20%27%27%2C%20%27styleConfig.VIDEO_SWITCH%27%3A%20%270%27%2C%20%27styleConfig.VIDEO_VIP_SWITCH%27%3A%20%270%27%2C%20%27styleConfig.BTN_AUTH_AUDIO%27%3A%20%271%27%2C%20%27styleConfig.BTN_AUTH_VIDEO%27%3A%20%271%27%2C%20%27styleConfig.BTN_AUTH_PIC%27%3A%20%271%27%2C%20%27styleConfig.BTN_AUTH_FILE%27%3A%20%271%27%2C%20%27styleConfig.BTN_AUTH_TIPS%27%3A%20%27%E9%9C%80%E8%A6%81%E4%BD%BF%E7%94%A8%E6%82%A8%E8%AE%BE%E5%A4%87%E7%9A%84%23btnName%23%E6%9D%83%E9%99%90%3Bneeds%20your%20device%20%23btnName%23auth%27%2C%20%27styleConfig.BTN_AUTH_SEARCH%27%3A%20%270%27%2C%20%27styleConfig.BTN_AUTH_DEBUG%27%3A%20%270%27%2C%20%27styleConfig.SHOW_END_NAV%27%3A%20%271%27%2C%20%27commonConfig.SHOW_HIS_MSG%27%3A%20%271%27%2C%20%27commonConfig.SHOW_HIS_MSG_RANGE%27%3A%20%270%27%2C%20%27commonConfig.OPEN_HOT_PIC%27%3A%20%271%27%2C%20%27commonConfig.CURR_CHANNLE_HIS_MSG%27%3A%20%270%27%2C%20%27commonConfig.OPEN_HOT_QUESTION%27%3A%20%271%27%2C%20%27commonConfig.HOT_QUESTION_SOURCE%27%3A%20%271%27%2C%20%27styleConfig.REALTIME_VOICE_FLAG%27%3A%20%271%27%2C%20%27styleConfig.REALTIME_VOICE_TYPE%27%3A%20%27wav%27%2C%20%27styleConfig.VOICE_TOWORD_URL%27%3A%20%27http%3A//**************%3A8088/cc-asr/interface/recognize2%3Fid%3D82533943160407566280717%27%2C%20%27styleConfig.ASR_CONFIG%27%3A%20%27%27%2C%20%27styleConfig.REALTIME_VOICE_SECOND%27%3A%20%2710%27%2C%20%27styleConfig.BUSI_SCRIPT_URL%27%3A%20%27%27%2C%20%27styleConfig.ENABLE_CUST_LEAVE_MSG%27%3A%20%271%27%2C%20%27styleConfig.ROBOT_TO_AGENT_CONFIRM%27%3A%20%270%27%2C%20%27styleConfig.ROBOT_TO_AGENT_CONFIRM_MSG1%27%3A%20%27%E5%BD%93%E5%89%8D%E5%92%A8%E8%AF%A2%E4%BA%BA%E5%B7%A5%E5%AE%A2%E6%9C%8D%E4%BA%BA%E6%95%B0%E8%BF%87%E5%A4%9A%EF%BC%8C%E7%A1%AE%E8%AE%A4%E6%98%AF%E5%90%A6%E8%BD%AC%E4%BA%BA%E5%B7%A5%EF%BC%9F%27%2C%20%27styleConfig.ROBOT_TO_AGENT_CONFIRM_MSG2%27%3A%20%27%E5%BD%93%E5%89%8D%E4%B8%BA%E9%9D%9E%E5%B7%A5%E4%BD%9C%E6%97%B6%E9%97%B4%EF%BC%8C%E7%A1%AE%E8%AE%A4%E6%98%AF%E5%90%A6%E8%BD%AC%E4%BA%BA%E5%B7%A5%EF%BC%9F%27%2C%20%27styleConfig.H5_INFO_GATHER_FLAG%27%3A%20%270%27%2C%20%27styleConfig.H5_INFO_GATHER_URL%27%3A%20%27%27%2C%20%27styleConfig.H5_INFO_TYPE%27%3A%20%27%27%2C%20%27styleConfig.TEL_NUM%27%3A%20%27%27%2C%20%27styleConfig.showWelcome%27%3A%20%271%27%2C%20%27styleConfig.CHAT_INPUT_TIPS%27%3A%20%27%E8%AF%B7%E7%94%A8%E4%B8%80%E5%8F%A5%E8%AF%9D%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0%E9%97%AE%E9%A2%98%27%2C%20%27styleConfig.EXPECT_QUESTION_SOURCE%27%3A%20%271%27%2C%20%27styleConfig.TOP_NAV_QUESTION_SOURCE%27%3A%20%271%27%2C%20%27styleConfig.SHOOT_VIDEO%27%3A%20%270%27%2C%20%27styleConfig.WITHDRAM_MSG%27%3A%20%270%27%2C%20%27styleConfig.AUTO_INVITE_WEBSITE_USER%27%3A%20%270%27%2C%20%27styleConfig.AUTO_INVITE_WEBSITE_USER_SECONDS%27%3A%20%2730%27%7D
[2025-07-04 15:41:32] [base_api.py:51] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'Set-Cookie': 'JSESSIONID=349A3F315FAA0120791743147818425A; Path=/cc-media; HttpOnly', 'x-frame-options': 'SAMEORIGIN', 'X-Content-Type-Options': 'nosniff', 'X-XSS-Protection': '1; mode=block', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Fri, 04 Jul 2025 07:41:32 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: {"msg":"查询渠道信息失败","state":500}
