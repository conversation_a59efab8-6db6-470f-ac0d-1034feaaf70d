import pytest
from api.api_updates import APIUpdates

@pytest.fixture(scope='class')
def api_client(api_client):
    return APIUpdates(session=api_client.session)

def test_updates(api_client):
    res = api_client.updates(BEGIN_WORK_TIME1="08:30",END_WORK_TIME1="12:30",ids=["82486620298687314487863","82486635115547395327549"],keys=[1111,7001])
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)