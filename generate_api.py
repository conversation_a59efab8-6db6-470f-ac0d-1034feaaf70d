import argparse
import os
import yaml
import re

API_CLASS_TEMPLATE = '''from api.base_api import BaseApi\nfrom utils.utils import Utils\n\nclass API{name_camel}(BaseApi):\n    def __init__(self, session=None):\n        super().__init__()\n        if session:\n            self.session = session\n        self.yaml = Utils.read_yaml(r'{yaml_path}')\n\n    def {name}(self, **kwargs):\n        req = Utils.get_steps(self.yaml, '{name}')\n        # 支持参数动态替换\n        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):\n            for k, v in kwargs.items():\n                req['data']['data'][k] = v\n        response = self.send_request(**req)\n        return response.json()\n'''

TEST_TEMPLATE = '''import pytest\nfrom api.api_{name} import API{name_camel}\n\<EMAIL>(scope='class')\ndef api_client(api_client):\n    return API{name_camel}(session=api_client.session)\n\ndef test_{name}(api_client):\n    res = api_client.{name}()\n    # 断言可根据yaml validate自动生成\n    assert isinstance(res, dict)\n'''

# 单独的方法模板（用于添加到现有API类中）
API_METHOD_TEMPLATE = '''
    def {name}(self, **kwargs):
        req = Utils.get_steps(self.yaml, '{name}')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()'''

# 单独的测试用例模板（用于添加到现有测试文件中）
TEST_CASE_TEMPLATE = '''
def test_{name}(api_client):
    res = api_client.{name}()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)'''

# API客户端fixture模板（用于添加到现有测试文件中）
API_CLIENT_FIXTURE_TEMPLATE = '''
@pytest.fixture(scope='class')
def api_client(api_client):
    return API{name_camel}(session=api_client.session)'''

def snake_to_camel(s):
    return ''.join([w.capitalize() for w in s.split('_')])

def check_method_exists(file_path, method_name):
    """检查文件中是否已存在指定方法"""
    if not os.path.exists(file_path):
        return False

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 使用正则表达式检查方法是否存在
    pattern = rf'def\s+{re.escape(method_name)}\s*\('
    return bool(re.search(pattern, content))

def check_fixture_exists(file_path, fixture_name):
    """检查测试文件中是否已存在指定fixture"""
    if not os.path.exists(file_path):
        return False

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查fixture是否存在
    pattern = rf'@pytest\.fixture.*\ndef\s+{re.escape(fixture_name)}\s*\('
    return bool(re.search(pattern, content, re.DOTALL))

def check_import_exists(file_path, import_statement):
    """检查文件中是否已存在指定的import语句"""
    if not os.path.exists(file_path):
        return False

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    return import_statement in content

def add_method_to_api_file(file_path, method_code):
    """在API文件的类中添加新方法"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 找到类的最后一个方法或__init__方法的结束位置
    # 在类的末尾添加新方法（在最后一个缩进块之后）
    lines = content.split('\n')
    insert_index = len(lines)

    # 从后往前找，找到最后一个有内容的行
    for i in range(len(lines) - 1, -1, -1):
        if lines[i].strip():
            insert_index = i + 1
            break

    # 插入新方法
    lines.insert(insert_index, method_code)

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))

def add_test_case_to_file(file_path, test_code, import_statement=None):
    """在测试文件中添加新的测试用例"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 如果需要添加import语句且不存在
    if import_statement and not check_import_exists(file_path, import_statement):
        lines = content.split('\n')
        # 找到最后一个import语句的位置
        import_insert_index = 0
        for i, line in enumerate(lines):
            if line.startswith('import ') or line.startswith('from '):
                import_insert_index = i + 1

        lines.insert(import_insert_index, import_statement)
        content = '\n'.join(lines)

    # 在文件末尾添加测试用例
    if not content.endswith('\n'):
        content += '\n'
    content += test_code

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def main():
    parser = argparse.ArgumentParser(description='根据yaml接口定义自动生成api和测试用例')
    parser.add_argument('yaml_path', help='yaml文件路径')
    parser.add_argument('--name', required=True, help='接口步骤name')
    parser.add_argument('--no-api', action='store_true', help='不生成新的API文件')
    parser.add_argument('--no-test', action='store_true', help='不生成新的测试文件')
    parser.add_argument('--api-file', help='指定现有的API文件路径，在其中添加方法')
    parser.add_argument('--test-file', help='指定现有的测试文件路径，在其中添加测试用例')
    args = parser.parse_args()

    yaml_path = args.yaml_path
    name = args.name
    name_camel = snake_to_camel(name)

    # 读取yaml，检查name是否存在
    with open(yaml_path, encoding='utf-8') as f:
        yml = yaml.safe_load(f)
    found = False
    for step in yml.get('teststeps', []):
        if step.get('name') == name:
            found = True
            break
    if not found:
        print(f"未找到name为{name}的接口定义！")
        return

    # 处理API文件
    if not args.no_api:
        if args.api_file:
            # 在指定的现有API文件中添加方法
            if os.path.exists(args.api_file):
                if not check_method_exists(args.api_file, name):
                    method_code = API_METHOD_TEMPLATE.format(name=name)
                    add_method_to_api_file(args.api_file, method_code)
                    print(f'已在 {args.api_file} 中添加方法: {name}')
                else:
                    print(f'方法 {name} 已存在于 {args.api_file} 中，跳过生成')
            else:
                print(f'指定的API文件 {args.api_file} 不存在！')
        else:
            # 生成新的API文件
            api_file = f'api/api_{name}.py'
            if os.path.exists(api_file):
                print(f'API文件 {api_file} 已存在，跳过生成（使用 --api-file 参数指定现有文件来添加方法）')
            else:
                api_code = API_CLASS_TEMPLATE.format(
                    name=name,
                    name_camel=name_camel,
                    yaml_path=yaml_path.replace('\\', '/'),
                )
                os.makedirs('api', exist_ok=True)
                with open(api_file, 'w', encoding='utf-8') as f:
                    f.write(api_code)
                print(f'已生成: {api_file}')

    # 处理测试文件
    if not args.no_test:
        if args.test_file:
            # 在指定的现有测试文件中添加测试用例
            if os.path.exists(args.test_file):
                # 检查是否需要添加import语句
                import_statement = f'from api.api_{name} import API{name_camel}'

                # 检查是否需要添加fixture
                if not check_fixture_exists(args.test_file, 'api_client'):
                    fixture_code = API_CLIENT_FIXTURE_TEMPLATE.format(name_camel=name_camel)
                    add_test_case_to_file(args.test_file, fixture_code, import_statement)
                    print(f'已在 {args.test_file} 中添加fixture: api_client')

                # 添加测试用例
                if not check_method_exists(args.test_file, f'test_{name}'):
                    test_case_code = TEST_CASE_TEMPLATE.format(name=name)
                    add_test_case_to_file(args.test_file, test_case_code, import_statement)
                    print(f'已在 {args.test_file} 中添加测试用例: test_{name}')
                else:
                    print(f'测试用例 test_{name} 已存在于 {args.test_file} 中，跳过生成')
            else:
                print(f'指定的测试文件 {args.test_file} 不存在！')
        else:
            # 生成新的测试文件
            test_file = f'testcases/test_{name}.py'
            if os.path.exists(test_file):
                print(f'测试文件 {test_file} 已存在，跳过生成（使用 --test-file 参数指定现有文件来添加测试用例）')
            else:
                test_code = TEST_TEMPLATE.format(
                    name=name,
                    name_camel=name_camel,
                )
                os.makedirs('testcases', exist_ok=True)
                with open(test_file, 'w', encoding='utf-8') as f:
                    f.write(test_code)
                print(f'已生成: {test_file}')

if __name__ == '__main__':
    main() 