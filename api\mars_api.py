from api.base_api import BaseApi
from utils.utils import Utils
import json


class MARSAPI(BaseApi):
    def __init__(self, session=None):
        super().__init__()
        if session:
            self.session = session

    def login(self, username: str, password: str) -> dict:
        """
        登录接口
        :param username: 登录名
        :param password: 密码
        :return: 登录响应结果
        """
        # 获取登录请求参数
        req = Utils.get_steps(Utils.read_yaml("../data/login.yml"), "login")

        # 替换用户名和密码
        req["data"]["data"] = Utils.key_replace(req["data"]["data"], {
            'j_username': Utils.get_bs64(username),
            'j_password': password
        })

        # 发送登录请求
        response = self.send_request(**req)
        return response.json()
