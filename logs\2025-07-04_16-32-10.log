[2025-07-04 16:32:11] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-04 16:32:11] [base_api.py:40] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/yc-login/login
- 请求头: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'login'}
- 请求体: {'data': '{"j_username": "ODAwMkBla2Y=", "j_password": "2R72jyzx63c8yTH54fCzxQ==", "j_imagecode": "", "callbackUrl": "", "busiId": "007", "loginType": "default", "versionDate": "20220920"}'}
- JSON数据: None
[2025-07-04 16:32:11] [base_api.py:67] [base_api:_handle_form_data] [INFO]- dumps后请求体: {"data": "{\"j_username\": \"ODAwMkBla2Y=\", \"j_password\": \"2R72jyzx63c8yTH54fCzxQ==\", \"j_imagecode\": \"\", \"callbackUrl\": \"\", \"busiId\": \"007\", \"loginType\": \"default\", \"versionDate\": \"20220920\"}"}
[2025-07-04 16:32:11] [base_api.py:52] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'x-frame-options': 'SAMEORIGIN, SAMEORIGIN', 'X-Content-Type-Options': 'nosniff, nosniff', 'X-XSS-Protection': '1; mode=block, 1; mode=block', 'Content-Security-Policy': 'none', 'Access-Control-Allow-Methods': 'GET,POST', 'Set-Cookie': 'JSESSIONID=5CBAFBF4E73B09B7B32A597832D2A4D5; Path=/yc-login; HttpOnly', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Fri, 04 Jul 2025 08:32:11 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: {"msg":"账号或密码不能为空！","state":0}
[2025-07-04 16:32:11] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-04 16:32:11] [base_api.py:40] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/cc-media/servlet/satisf
- 请求头: {'Accept': 'application/json, text/javascript, */*; q=0.01', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9', 'Connection': 'keep-alive', 'Content-Length': '3014', 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'Host': '**************:9060', 'Origin': 'http://**************:9060', 'Referer': 'http://**************:9060/cc-media/pages/media/channel-satisfy-edit.html?channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&channelId=82486620298687314487863', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'updateChannelSatisf'}
- 请求体: {'data': {'manual': {'satisfyList': [{'NAME': '非常满意', 'CODE': '1', 'ITEM_TYPE': '1', 'detail': [{'NAME': '服务态度', 'CODE': '1', 'SORT_NUM': '1', 'ITEM2_TYPE': '1'}], 'detail2': [{'NAME': '服务效率', 'CODE': '123we', 'SORT_NUM': '1'}]}, {'NAME': '满意', 'CODE': '2', 'ITEM_TYPE': '1', 'detail': [{'NAME': '专业水平', 'CODE': '2', 'SORT_NUM': '2', 'ITEM2_TYPE': '1'}], 'detail2': [{'NAME': '服务效率', 'CODE': '123we', 'SORT_NUM': '1'}]}, {'NAME': '一般', 'CODE': '3', 'ITEM_TYPE': '2', 'detail': [{'NAME': '沟通表达', 'CODE': '3', 'SORT_NUM': '3', 'ITEM2_TYPE': '1'}], 'detail2': [{'NAME': '服务效率', 'CODE': '123we', 'SORT_NUM': '1'}]}, {'NAME': '对结果不满意', 'CODE': '4', 'ITEM_TYPE': '3', 'detail': [{'NAME': '响应速度', 'CODE': '4', 'SORT_NUM': '4', 'ITEM2_TYPE': '1'}], 'detail2': [{'NAME': '服务效率', 'CODE': '123we', 'SORT_NUM': '1'}]}, {'NAME': '对服务不满意', 'CODE': '5', 'ITEM_TYPE': '3', 'detail': [{'NAME': '响应速度', 'CODE': '4', 'SORT_NUM': '4', 'ITEM2_TYPE': '1'}], 'detail2': [{'NAME': '服务效率', 'CODE': '123we', 'SORT_NUM': '1'}]}], 'startWord': '您好，对刚才客服的解答还满意么？麻烦您为客服回个评价数字哟，您的回复非常重要哦', 'endWord': '您的评价是我们进步的动力，谢谢。'}, 'robot': {'satisfyList': []}, 'third': {'satisfyList': []}, 'video': {'satisfyList': []}, 'channelId': '82486620298687314487863', 'channelNo': '1111', 'channelName': '%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5'}}
- JSON数据: None
[2025-07-04 16:32:11] [base_api.py:67] [base_api:_handle_form_data] [INFO]- dumps后请求体: {"data": {"manual": {"satisfyList": [{"NAME": "\u975e\u5e38\u6ee1\u610f", "CODE": "1", "ITEM_TYPE": "1", "detail": [{"NAME": "\u670d\u52a1\u6001\u5ea6", "CODE": "1", "SORT_NUM": "1", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}, {"NAME": "\u6ee1\u610f", "CODE": "2", "ITEM_TYPE": "1", "detail": [{"NAME": "\u4e13\u4e1a\u6c34\u5e73", "CODE": "2", "SORT_NUM": "2", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}, {"NAME": "\u4e00\u822c", "CODE": "3", "ITEM_TYPE": "2", "detail": [{"NAME": "\u6c9f\u901a\u8868\u8fbe", "CODE": "3", "SORT_NUM": "3", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}, {"NAME": "\u5bf9\u7ed3\u679c\u4e0d\u6ee1\u610f", "CODE": "4", "ITEM_TYPE": "3", "detail": [{"NAME": "\u54cd\u5e94\u901f\u5ea6", "CODE": "4", "SORT_NUM": "4", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}, {"NAME": "\u5bf9\u670d\u52a1\u4e0d\u6ee1\u610f", "CODE": "5", "ITEM_TYPE": "3", "detail": [{"NAME": "\u54cd\u5e94\u901f\u5ea6", "CODE": "4", "SORT_NUM": "4", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}], "startWord": "\u60a8\u597d\uff0c\u5bf9\u521a\u624d\u5ba2\u670d\u7684\u89e3\u7b54\u8fd8\u6ee1\u610f\u4e48\uff1f\u9ebb\u70e6\u60a8\u4e3a\u5ba2\u670d\u56de\u4e2a\u8bc4\u4ef7\u6570\u5b57\u54df\uff0c\u60a8\u7684\u56de\u590d\u975e\u5e38\u91cd\u8981\u54e6", "endWord": "\u60a8\u7684\u8bc4\u4ef7\u662f\u6211\u4eec\u8fdb\u6b65\u7684\u52a8\u529b\uff0c\u8c22\u8c22\u3002"}, "robot": {"satisfyList": []}, "third": {"satisfyList": []}, "video": {"satisfyList": []}, "channelId": "82486620298687314487863", "channelNo": "1111", "channelName": "%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5"}}
[2025-07-04 16:32:11] [base_api.py:52] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'X-Content-Type-Options': 'nosniff', 'Content-Security-Policy': 'none', 'X-XSS-Protection': '1; mode=block', 'X-FRAME-OPTIONS': 'SAMEORIGIN', 'Access-Control-Allow-Methods': 'GET,POST', 'Accept-Ranges': 'bytes', 'ETag': 'W/"24950-1748950146000"', 'Last-Modified': 'Tue, 03 Jun 2025 11:29:06 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'text/html;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Fri, 04 Jul 2025 08:32:11 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: <!DOCTYPE html>

<html>



<head>

    <meta charset="utf-8">

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <meta name="renderer" content="webkit" />

    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

    <meta name="wap-font-scale" content="no" />

    <meta name="viewport" content="initial-scale=1, maximum-scale=1">

    <LINK rel="Bookmark" href="/yc-login/favicon.ico" />

    <LINK rel="Shortcut Icon" href="/yc-login/favicon.ico" />

    <title i18n-content="登录"></title>

    <link rel="stylesheet" href="/cc-portal/static/css/login_yq.css">

    <link rel="stylesheet" href="/cc-base/static/cdn/element-ui@2.15.6/index.css">

    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">

    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">

    <style>

        input:-webkit-autofill {

            -webkit-box-shadow: 0 0 0px 1000px rgba(242, 244, 247) inset !important;

            -webkit-text-fill-color: #222;

            caret-color: #222;

        }

    </style>

</head>



<body class="page-login">

    <div class="base_login" id="loginPage" v-cloak>

        <div class="base_bg" id="lottie_box"></div>

        <div class="base_bg" id="lottie_box2"></div>

        <div class="login_boxs">

            <!-- 账号密码登录区域 -->

            <div class="login_area" v-show="!isScanMode && !isForgetMode">

                <img class="_login_logo" :src="cfg.loginLogo" :alt="getI18nValue('登录logo')">

                <div class="_login_input_box">

                    <input id="busiId" type="hidden" value="007" />

                    <input id="ctxPath" type="hidden" value="/cc-portal" />

                    <input type="text" :placeholder="getI18nValue('请输入用户名')" v-model="username" id="j_username" />

                    <div class="slide"></div>

                    <div class="icon">

                        <img src="/cc-portal/static/images/icon/user.png" :alt="getI18nValue('用户图标')">

                    </div>

                </div>

                <div class="_login_input_box">

                    <input :placeholder="getI18nValue('请输入密码')" :type="showPassword ? 'text' : 'password'"

                        v-model="password" id="j_password" />

                    <div class="slide"></div>

                    <div class="icon">

                        <img src="/cc-portal/static/images/icon/unlock.png" :alt="getI18nValue('密码图标')">

                    </div>

                    <div class="icon right">

                        <img @click="showPassword = !showPassword" v-if="showPassword"

                            src="/cc-portal/static/images/icon/eye.png" :alt="getI18nValue('显示密码')">

                        <img @click="showPassword = !showPassword" v-else

                            src="/cc-portal/static/images/icon/eye-invisible.png" :alt="getI18nValue('隐藏密码')">

                    </div>

                </div>

                <div class="verify_box">

                    <div class="verify_input">

                        <input type="text" :placeholder="getI18nValue('请输入验证码')" v-model="verifyCode"

                            id="j_imagecode" />

                    </div>

                    <div class="verify_img">

                        <img id="imageCode" style="cursor: pointer;" @click="reloadImageCode();" i18n-title="点击刷新验证码"

                            :src="codeUrl" :alt="getI18nValue('验证码')">

                    </div>

                </div>

                <div class="login_btn">

                    <el-button type="primary" @click="submitCheck()" i18n-content="登录"></el-button>

                </div>

            </div>



            <!-- 扫码登录区域 -->

            <div class="scan_area" v-show="isScanMode && !isForgetMode">

                <img class="_login_logo" :src="cfg.loginLogo" :alt="getI18nValue('登录logo')">

                <div class="qrcode_box">

                    <img :src="qrcodeUrl" :alt="getI18nValue('二维码')" @click="refreshQrcode">

                    <div class="qrcode_status" v-show="qrcodeStatus.show">

                        <p class="text">{{getI18nValue(qrcodeStatus.text)}}</p>

                        <el-button v-if="qrcodeStatus.needRefresh" @click="refreshQrcode" i18n-content="刷新"></el-button>

                        <img v-if="qrcodeStatus.success" src="/cc-portal/static/images/correct.png" class="success_icon"

                            :alt="getI18nValue('成功')">

                    </div>

                    <div class="scan_tips">

                        <img src="/cc-portal/static/images/scan.png" :alt="getI18nValue('扫描')">

                        <span i18n-content="打开移动坐席APP扫描二维码"></span>

                    </div>

                </div>

            </div>



            <!-- 忘记密码区域 -->

            <div class="forget_pwd_area" v-show="isForgetMode">

                <img class="_login_logo" :src="cfg.loginLogo" :alt="getI18nValue('登录logo')">

                <div class="_login_input_box">

                    <input type="text" :placeholder="getI18nValue('请输入用户账号')" v-model="forgetPwd.userAcc">

                    <div class="slide"></div>

                    <div class="icon">

                        <img src="/cc-portal/static/images/icon/user.png" :alt="getI18nValue('用户图标')">

                    </div>

                </div>

                <div class="_login_input_box">

                    <input type="text" :placeholder="getI18nValue('请输入预留的手机号或邮箱地址')" v-model="forgetPwd.userPhone">

                    <div class="slide"></div>

                    <div class="icon">

                        <img src="/cc-portal/static/images/icon/phone.png" :alt="getI18nValue('手机图标')">

                    </div>

                </div>

                <div class="verify_box">

                    <div class="verify_input">

                        <input type="text" :placeholder="getI18nValue('请输入图形验证码')" v-model="forgetPwd.randCode">

                    </div>

                    <div class="verify_img">

                        <img :src="forgetPwd.imageCodeUrl" @click="getPwdImageCode" :alt="getI18nValue('验证码')">

                    </div>

                </div>

                <div class="verify_box">

                    <div class="verify_input">

                        <input type="text" :placeholder="getI18nValue('请输入验证码')" v-model="forgetPwd.code">

                    </div>

                    <div class="verify_img">

                        <el-button :disabled="!forgetPwd.canGetCode" @click="forgetPwdGetCode" class="get_code_btn">

                            {{getI18nValue(forgetPwd.codeText)}}

                        </el-button>

                    </div>

                </div>

                <div class="_login_input_box" v-if="forgetPwd.showPwd">

                    <input :type="showPassword ? 'text' : 'password'" :placeholder="getI18nValue('请输入新密码')"

                        v-model="forgetPwd.newPwd">

                    <div class="slide"></div>

                    <div class="icon">

                        <img src="/cc-portal/static/images/icon/unlock.png" :alt="getI18nValue('密码图标')">

                    </div>

                    <div class="icon right">

                        <img @click="showPassword = !showPassword"

                            :src="showPassword ? '/cc-portal/static/images/icon/eye.png' : '/cc-portal/static/images/icon/eye-invisible.png'"

                            :alt="getI18nValue(showPassword ? '显示密码' : '隐藏密码')">

                    </div>

                </div>

                <div class="login_btn">

                    <el-button type="primary" :disabled="!forgetPwd.canSubmit" @click="forgetPwdSubmit"

                        i18n-content="确定"></el-button>

                </div>

            </div>



            <!-- 底部切换按钮 -->

            <div class="mode_switch">

                <span class="switch_btn" v-if="cfg.userFindPwd === 'Y'" @click="toggleForgetMode">

                    {{isForgetMode ? getI18nValue('返回登录') : getI18nValue('忘记密码')}}

                </span>

                <span class="switch_btn" v-if="cfg.scanCodeLogin === 'Y'" @click="toggleScanMode">

                    {{isScanMode ? getI18nValue('密码登录') : getI18nValue('扫码登录')}}

                </span>

            </div>

        </div>

        <div class="login_footer">

            <div class="login_footer_text">{{getI18nValue(cfg.copyRight)}}</div>

            <div class="login_footer_text">{{getI18nValue(cfg.browserVersion)}}</div>

        </div>

    </div>

    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>

    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>

    <script src="/cc-portal/static/js/lottie.min.js"></script>

    <script type="text/javascript" src="/yc-login/static/js/jquery.min.js"></script>

    <script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>

    <script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>

    <script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js"></script>

    <script type="text/javascript" src="/easitline-static/lib/jquery/jquery.md5.js"></script>

    <script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>

    <script type="text/javascript" src="/easitline-static/lib/jquery/jquery.base64.min.js"></script>

    <script type="text/javascript" src="/yc-login/static/js/login.js?v=20240716"></script>

    <script type="text/javascript" src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>

    <script type="text/javascript" src="/cc-portal/static/js/my_i18n.js?2020071711"></script>

    <script type="text/javascript" src="/cc-base/static/js/i18n.js?2020071711"></script>

    <!-- <script type="text/javascript">

        if (window != top)

            top.location.replace(location.href);

    </script> -->

    <script type="text/javascript">

    </script>

    <script>

        var loginPage = new Vue({

            el: '#loginPage',

            data: {

                username: '',

                password: '',

                verifyCode: '',

                st: '',

                showPassword: false,

                cfg: {

                    "scanCodeLogin": "Y",

                    "copyRight": getI18nValue("云趣科技版权所有"),

                    "loginLogo": "/cc-portal/static/images/logo.png",

                    "browserVersion": getI18nValue("浏览器推荐使用:谷歌(Chrome) 版本,大于1440X900分辩率!"),

                    "userFindPwd": "Y"

                },

                isScanMode: false,

                isForgetMode: false,

                qrcodeUrl: '',

                qrcodeStatus: {

                    show: false,

                    text: '',

                    needRefresh: false,

                    success: false

                },

                qrcodeTimer: null,

                forgetPwd: {

                    userAcc: '',

                    randCode: '',

                    userPhone: '',

                    code: '',

                    newPwd: '',

                    imageCodeUrl: '',

                    codeText: '获取验证码',

                    canGetCode: false,

                    canSubmit: false,

                    showPwd: false,

                    timer: null,

                    countdown: 120

                }

            },

            computed: {

                codeUrl: function () {

                    return `/yc-login/login?login=ImageCode&st=${this.st}`;

                }

            },

            mounted() {

                this.st = new Date().getTime();

                lottie.loadAnimation({

                    container: document.getElementById('lottie_box'),                                                                          // 需要绑定的div

                    renderer: 'svg',                                                                                                           // 渲染方式:svg：支持交互、不会失帧、canvas、html：支持3D，支持交互

                    loop: true,                                                                                                                // 循环播放，默认：true

                    autoplay: true,                                                                                                            // 自动播放 ，默认true

                    path: '/cc-portal/static/json/背景动效.json',             // 现在用的线上json 路径(或者自己有json动画文件修改自己本地路径)

                    rendererSettings: {

                        preserveAspectRatio: 'none',

                    }

                })

                lottie.loadAnimation({

                    container: document.getElementById('lottie_box2'),                                                                          // 需要绑定的div

                    renderer: 'svg',                                                                                                           // 渲染方式:svg：支持交互、不会失帧、canvas、html：支持3D，支持交互

                    loop: true,                                                                                                                // 循环播放，默认：true

                    autoplay: true,                                                                                                            // 自动播放 ，默认true

                    path: '/cc-portal/static/json/核心图形.json',             // 现在用的线上json 路径(或者自己有json动画文件修改自己本地路径)

                    rendererSettings: {

                        preserveAspectRatio: 'none',

                    }

                })

                yq.remoteCall('/cc-portal/api?action=loginCfg', '').then(res => {

                    if (res.state == 1) {

                        this.cfg = Object.assign(this.cfg, res.data)

                    }

                })

            },

            methods: {

                reloadImageCode() {

                    this.st = new Date().getTime();

                },

                toggleScanMode() {

                    if (this.isForgetMode) {

                        this.isForgetMode = false;

                    }

                    this.isScanMode = !this.isScanMode;



                    if (this.isScanMode) {

                        this.refreshQrcode();

                        this.startQrcodeCheck();

                    } else {

                        this.stopQrcodeCheck();

                    }

                },

                toggleForgetMode() {

                    if (this.isScanMode) {

                        this.isScanMode = false;

                        this.stopQrcodeCheck();

                    }

                    this.isForgetMode = !this.isForgetMode;



                    if (this.isForgetMode) {

                        this.getPwdImageCode();

                    } else {

                        this.resetForgetPwd();

                    }

                },

                refreshQrcode() {

                    this.qrcodeUrl = '/yc-ssologin/qrCode/qrCodeLogin?action=getQrCode&t=' + new Date().getTime();

                    this.qrcodeStatus.show = false;

                },

                startQrcodeCheck() {

                    this.qrcodeTimer = setInterval(() => {

                        this.checkQrcodeStatus();

                    }, 2000);

                },

                stopQrcodeCheck() {

                    if (this.qrcodeTimer) {

                        clearInterval(this.qrcodeTimer);

                        this.qrcodeTimer = null;

                    }

                },

                checkQrcodeStatus() {

                    yq.remoteCall('/yc-ssologin/qrCode/qrCodeLogin?action=getQrCodeLoginResult', {})

                        .then(res => {

                            if (res.state === 1) {

                                this.qrcodeStatus.show = true;

                                this.qrcodeStatus.success = true;

                                this.stopQrcodeCheck();

                                setTimeout(() => {

                                    location.replace(res.data);

                                }, 1000);

                            } else if (res.state === 502) {

                                this.qrcodeStatus.show = true;

                                this.qrcodeStatus.text = '二维码已失效';

                                this.qrcodeStatus.needRefresh = true;

                                this.stopQrcodeCheck();

                            }

                        });

                },

                getPwdImageCode() {

                    this.forgetPwd.imageCodeUrl = '/cc-base/api/spec?action=imageCode&v=' + new Date().getTime();

                    this.forgetPwd.randCode = '';

                },

                checkForgetPwdForm() {

                    // 检查是否可以获取验证码

                    if (!this.forgetPwd.timer) {

                        const canGetCode = this.forgetPwd.userAcc &&

                            this.forgetPwd.userPhone &&

                            this.forgetPwd.randCode;

                        this.forgetPwd.canGetCode = canGetCode;

                    }



                    // 检查是否可以提交

                    if (this.forgetPwd.userAcc &&

                        this.forgetPwd.userPhone &&

                        this.forgetPwd.randCode &&

                        this.forgetPwd.code) {

                        this.forgetPwd.showPwd = true;

                        if (this.forgetPwd.newPwd) {

                            this.forgetPwd.canSubmit = true;

                        } else {

                            this.forgetPwd.canSubmit = false;

                        }

                    } else {

                        this.forgetPwd.showPwd = false;

                        this.forgetPwd.canSubmit = false;

                    }

                },

                forgetPwdGetCode() {

                    if (!this.forgetPwd.userAcc || !this.forgetPwd.userPhone || !this.forgetPwd.randCode) {

                        this.$message.warning(getI18nValue('请填写完整信息'));

                        return;

                    }



                    const data = {

                        userAcc: this.forgetPwd.userAcc,

                        userPhone: this.forgetPwd.userPhone,

                        randCode: this.forgetPwd.randCode

                    };



                    yq.remoteCall('/cc-base/api/spec?action=forgetPwdGetCode', data)

                        .then(res => {

                            if (res.state === 1) {

                                this.startForgetPwdCountdown();

                                this.$message.success(res.msg || '验证码已发送');

                            } else {

                                this.$message.error(res.msg || '获取验证码失败');

                                this.getPwdImageCode();

                            }

                        })

                        .catch(err => {

                            this.$message.error('系统错误,请稍后重试');

                            this.getPwdImageCode();

                        });

                },

                startForgetPwdCountdown() {

                    this.forgetPwd.canGetCode = false;

                    this.forgetPwd.timer = setInterval(() => {

                        if (this.forgetPwd.countdown > 0) {

                            this.forgetPwd.codeText = getI18nValue('重新获取') + `(${this.forgetPwd.countdown}s)`;

                            this.forgetPwd.countdown--;

                        } else {

                            this.stopForgetPwdCountdown();

                        }

                    }, 1000);

                },

                stopForgetPwdCountdown() {

                    if (this.forgetPwd.timer) {

                        clearInterval(this.forgetPwd.timer);

                        this.forgetPwd.timer = null;

                    }

                    this.forgetPwd.countdown = 120;

                    this.forgetPwd.codeText = '获取验证码';

                    this.forgetPwd.canGetCode = true;

                },

                forgetPwdSubmit() {

                    if (!this.forgetPwd.userAcc ||

                        !this.forgetPwd.code ||

                        !this.forgetPwd.newPwd) {

                        this.$message.warning(getI18nValue('请填写完整信息'));

                        return;

                    }



                    // 密码强度验证

                    if (this.forgetPwd.newPwd.length < 6) {

                        this.$message.warning(getI18nValue('密码长度不能少于6位'));

                        return;

                    }



                    const data = {

                        userAcc: this.forgetPwd.userAcc,

                        code: this.forgetPwd.code,

                        newPwd: $.md5(this.forgetPwd.newPwd).toUpperCase(),

                        key: window.dataEncrypt ? window.dataEncrypt(this.forgetPwd.newPwd) : ''

                    };



                    yq.remoteCall('/cc-base/api/spec?action=forgetPwdSubmit', data)

                        .then(res => {

                            if (res.state === 1) {

                                this.$message.success(res.msg || '密码重置成功');

                                setTimeout(() => {

                                    location.reload();

                                }, 1500);

                            } else {

                                this.$message.error(res.msg || '密码重置失败');

                                this.getPwdImageCode();

                            }

                        })

                        .catch(err => {

                            this.$message.error('系统错误,请稍后重试');

                            this.getPwdImageCode();

                        });

                },

                resetForgetPwd() {

                    this.forgetPwd = {

                        userAcc: '',

                        randCode: '',

                        userPhone: '',

                        code: '',

                        newPwd: '',

                        imageCodeUrl: '',

                        codeText: '获取验证码',

                        canGetCode: false,

                        canSubmit: false,

                        showPwd: false,

                        timer: null,

                        countdown: 120

                    };

                    this.getPwdImageCode();

                },

                uploadSuccess(data) {

                    this.isSave = false;

                    this.$message({

                        message: getI18nValue(data.msg),

                        type: data.state == 1 ? 'success' : 'error'

                    });

                },

                uploadError(err) {

                    this.$message({

                        message: getI18nValue(err.msg),

                        type: 'error'

                    });

                    this.isSave = false;

                }

            },

            watch: {

                'forgetPwd.userAcc'(val) {

                    this.checkForgetPwdForm();

                },

                'forgetPwd.randCode'(val) {

                    this.checkForgetPwdForm();

                },

                'forgetPwd.userPhone'(val) {

                    this.checkForgetPwdForm();

                },

                'forgetPwd.code'(val) {

                    this.checkForgetPwdForm();

                },

                'forgetPwd.newPwd'(val) {

                    this.checkForgetPwdForm();

                }

            },

            beforeDestroy() {

                this.stopQrcodeCheck();

                this.stopForgetPwdCountdown();

            }

        })

    </script>

</body>



</html>
