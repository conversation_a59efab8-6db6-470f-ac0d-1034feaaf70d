[2025-07-04 15:57:31] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-04 15:57:31] [base_api.py:40] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/yc-login/login
- 请求头: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'login'}
- 请求体: {"data": "{\"j_username\": \"ODAwMkBla2Y=\", \"j_password\": \"2R72jyzx63c8yTH54fCzxQ==\", \"j_imagecode\": \"\", \"callbackUrl\": \"\", \"busiId\": \"007\", \"loginType\": \"default\", \"versionDate\": \"20220920\"}"}
- JSON数据: None
[2025-07-04 15:57:31] [base_api.py:52] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'x-frame-options': 'SAMEORIGIN, SAMEORIGIN', 'X-Content-Type-Options': 'nosniff, nosniff', 'X-XSS-Protection': '1; mode=block, 1; mode=block', 'Content-Security-Policy': 'none', 'Access-Control-Allow-Methods': 'GET,POST', 'Set-Cookie': 'JSESSIONID=A3CD597CD7C0880229B330A37CC4C2A3; Path=/yc-login; HttpOnly, JSESSIONID_NODE=6EB3C50D8EE1280098E3F533CA893FE2; Path=/; HttpOnly, mars_token=MAyxGLIO5nBMwEr38SD8P2Z8c6O1H6j+5fC+svxkk3clslBVC/w/70M4/tfQQ8CGidnB0cla00tww37bEBBujMBE3cPa5VHyTnDZgkRFpDw=; Max-Age=43200; Expires=Fri, 04 Jul 2025 19:57:31 GMT; Path=/; Secure; HttpOnly', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Fri, 04 Jul 2025 07:57:31 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: {"msg":"操作成功!","userEntId":"1000","data":[{"BUSI_ORDER_ID":"82546991020289016354594","BUSI_ID":"002","BUSI_NAME":"云电销(基础版)","APP_CONTEXT_PATH":"/yc-portal","BUSI_LOGO_URL":"slt-ydx.png","VALID_DATE":"2025-04-22","INVALID_DATE":"2031-04-22"},{"BUSI_ORDER_ID":"83880897233159997654981","BUSI_ID":"007","BUSI_NAME":"企业呼叫中心","APP_CONTEXT_PATH":"/cc-portal","BUSI_LOGO_URL":"slt-ykf.png","VALID_DATE":"2021-01-29","INVALID_DATE":"2025-01-30"},{"BUSI_ORDER_ID":"82575451131639974023157","BUSI_ID":"015","BUSI_NAME":"智能知识库","APP_CONTEXT_PATH":"/yc-alknowledge","BUSI_LOGO_URL":"slt-ykf.png","VALID_DATE":"2025-03-20","INVALID_DATE":"2031-03-20"},{"BUSI_ORDER_ID":"82552278394919165788429","BUSI_ID":"016","BUSI_NAME":"语音机器人","APP_CONTEXT_PATH":"/mk-robotvue","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-04-16","INVALID_DATE":"2031-04-16"},{"BUSI_ORDER_ID":"82568438201939908172984","BUSI_ID":"018","BUSI_NAME":"智能分析平台","APP_CONTEXT_PATH":"/yc-aianalysis","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-03-28","INVALID_DATE":"2030-03-28"},{"BUSI_ORDER_ID":"82552095494469152320151","BUSI_ID":"201","BUSI_NAME":"智能外呼","APP_CONTEXT_PATH":"/yc-call","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-04-16","INVALID_DATE":"2031-04-16"}],"busiId":"007","userAccount":"8002@ekf","loginTimes":"908","userEntName":"企业呼叫中心","state":1,"busiOrderId":"83880897233159997654981","userId":"83880896957889997330198"}
[2025-07-04 15:57:31] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-04 15:57:31] [base_api.py:40] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/cc-media/servlet/satisf
- 请求头: {'Accept': 'application/json, text/javascript, */*; q=0.01', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9', 'Connection': 'keep-alive', 'Content-Length': '3014', 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'Host': '**************:9060', 'Origin': 'http://**************:9060', 'Referer': 'http://**************:9060/cc-media/pages/media/channel-satisfy-edit.html?channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&channelId=82486620298687314487863', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'updateChannelSatisf'}
- 请求体: {"data": {"manual": {"satisfyList": [{"NAME": "\u975e\u5e38\u6ee1\u610f", "CODE": "1", "ITEM_TYPE": "1", "detail": [{"NAME": "\u670d\u52a1\u6001\u5ea6", "CODE": "1", "SORT_NUM": "1", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}, {"NAME": "\u6ee1\u610f", "CODE": "2", "ITEM_TYPE": "1", "detail": [{"NAME": "\u4e13\u4e1a\u6c34\u5e73", "CODE": "2", "SORT_NUM": "2", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}, {"NAME": "\u4e00\u822c", "CODE": "3", "ITEM_TYPE": "2", "detail": [{"NAME": "\u6c9f\u901a\u8868\u8fbe", "CODE": "3", "SORT_NUM": "3", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}, {"NAME": "\u5bf9\u7ed3\u679c\u4e0d\u6ee1\u610f", "CODE": "4", "ITEM_TYPE": "3", "detail": [{"NAME": "\u54cd\u5e94\u901f\u5ea6", "CODE": "4", "SORT_NUM": "4", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}, {"NAME": "\u5bf9\u670d\u52a1\u4e0d\u6ee1\u610f", "CODE": "5", "ITEM_TYPE": "3", "detail": [{"NAME": "\u54cd\u5e94\u901f\u5ea6", "CODE": "4", "SORT_NUM": "4", "ITEM2_TYPE": "1"}], "detail2": [{"NAME": "\u670d\u52a1\u6548\u7387", "CODE": "123we", "SORT_NUM": "1"}]}], "startWord": "\u60a8\u597d\uff0c\u5bf9\u521a\u624d\u5ba2\u670d\u7684\u89e3\u7b54\u8fd8\u6ee1\u610f\u4e48\uff1f\u9ebb\u70e6\u60a8\u4e3a\u5ba2\u670d\u56de\u4e2a\u8bc4\u4ef7\u6570\u5b57\u54df\uff0c\u60a8\u7684\u56de\u590d\u975e\u5e38\u91cd\u8981\u54e6", "endWord": "\u60a8\u7684\u8bc4\u4ef7\u662f\u6211\u4eec\u8fdb\u6b65\u7684\u52a8\u529b\uff0c\u8c22\u8c22\u3002"}, "robot": {"satisfyList": []}, "third": {"satisfyList": []}, "video": {"satisfyList": []}, "channelId": "82486620298687314487863", "channelNo": "1111", "channelName": "%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5"}}
- JSON数据: None
[2025-07-04 15:57:31] [base_api.py:52] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 500
- 响应头: {'Set-Cookie': 'JSESSIONID=A7E3D42F16FFE1EA61223F3F50B594CB; Path=/cc-media; HttpOnly', 'x-frame-options': 'SAMEORIGIN', 'X-Content-Type-Options': 'nosniff', 'X-XSS-Protection': '1; mode=block', 'Content-Type': 'text/html;charset=utf-8', 'Content-Length': '1058', 'Date': 'Fri, 04 Jul 2025 07:57:31 GMT', 'Connection': 'close'}
- 响应体: <!DOCTYPE html>

<html>

	<head>

		<title>系统发生错误 -500 </title>

		<link rel="icon" type="image/x-icon" href="favicon.ico">

		<style type="text/css">

			.body {

			  color: #666;

			  text-align: center;

			  font-family: Helvetica, 'microsoft yahei', Arial, sans-serif;

			  margin:0;

			  width: 800px;

			  margin: auto;

			  font-size: 14px;

			}

			h1 {

			  font-size: 56px;

			  font-weight: normal;

			  color: #456;

			}

			h2 { font-size: 24px; color: #666; line-height: 1.5em; }

			

			h3 {

			  color: #456;

			  font-size: 20px;

			  font-weight: normal;

			  line-height: 28px;

			}

			

			hr {

			  margin: 18px 0;

			  border: 0;

			  border-top: 1px solid #EEE;

			  border-bottom: 1px solid white;

			}

			

			a{

			    color: #17bc9b;

			    text-decoration: none;

			}

		</style>

	</head>

<body class="body">

 	<h1>500</h1>

  <h3>系统发生错误.</h3>

  <hr/>

  <p>如果问题持续存在，请与我们联系。 <br><br><a href="/cc-media">返回首页</a></p>

	</body>

</html>
