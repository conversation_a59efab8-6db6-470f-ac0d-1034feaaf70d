config:
    name: "登录"
    variables: {}
teststeps:
-   name: "login"
    describe: "登录接口"
    request:
        data:
            data: '{"j_username":"ODAwMkBla2Y=","j_password":"+EeQTKbJUpLd7KWuylA4MXSqpP0J8nTOMvdwYsedjArV4952Um17BAviXf/k2kK/","j_imagecode":"","callbackUrl":"","busiId":"007","loginType":"default","versionDate":"20220920"}'
        headers:
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
                (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36
            X-Requested-With: XMLHttpRequest
            token: bWFyc0AyMDE5
        method: POST
        params:
            action: login
        url: /yc-login/login
    validate:
    -   eq:
        - status_code
        - 200
    -   eq:
        - headers.Content-Type
        - application/json;charset=UTF-8
    -   eq:
        - content.msg
        - 操作成功!
    -   eq:
        - content.userEntId
        - '1000'
    -   eq:
        - content.busiId
        - '007'
    -   eq:
        - content.userAccount
        - 8002@ekf
    -   eq:
        - content.loginTimes
        - '71'
    -   eq:
        - content.userEntName
        - 企业呼叫中心
    -   eq:
        - content.state
        - 1
    -   eq:
        - content.busiOrderId
        - '83880897233159997654981'
    -   eq:
        - content.userId
        - '83880896957889997330198'
-   name: "login_authen"
    request:
        headers:
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
                (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36
        method: GET
        params:
            action: success
            busiId: '007'
            ctxPath: /cc-portal
        url: /yc-login/authen
    validate:
    -   eq:
        - status_code
        - 302