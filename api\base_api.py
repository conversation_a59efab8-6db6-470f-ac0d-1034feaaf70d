from urllib.parse import quote
import requests
from utils.logger import logger
import json

class BaseApi:
    def __init__(self):
        self.session = requests.session()

    def send_request(self, method, url, **kwargs):
        """
        发送HTTP请求的通用方法
        :param method: 请求方法
        :param url: 请求URL
        :param kwargs: 请求参数
        :return: 响应对象
        """
        try:
            # 记录请求信息
            self._log_request(method, url, **kwargs)

            # 处理 form-urlencoded 数据
            self._handle_form_data(kwargs)

            # 发送请求
            response = self.session.request(method, url, **kwargs)

            # 记录响应信息
            self._log_response(response)

            return response

        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            raise

    def _log_request(self, method, url, **kwargs):
        """记录请求信息"""
        logger.info(
            f"\n请求信息:\n"
            f"- 方法: {method}\n"
            f"- URL: {url}\n"
            f"- 请求头: {kwargs.get('headers')}\n"
            f"- 查询参数: {kwargs.get('params')}\n"
            f"- 请求体: {kwargs.get('data')}\n"
            f"- JSON数据: {kwargs.get('json')}"
        )

    def _log_response(self, response):
        """记录响应信息"""
        logger.info(
            f"\n响应信息:\n"
            f"- 状态码: {response.status_code}\n"
            f"- 响应头: {dict(response.headers)}\n"
            f"- 响应体: {response.text}"
        )

    def _handle_form_data(self, kwargs):
        """处理 form-urlencoded 数据"""
        if not kwargs.get('data'):
            return

        headers = kwargs.get('headers', {})
        content_type = headers.get('Content-Type', '').lower()


        # 处理字典类型的 data
        if isinstance(kwargs['data'], dict):
            if 'application/x-www-form-urlencoded' in content_type:
                # 特殊处理：如果字典中包含嵌套的JSON字符串，需要单独处理
                if any(isinstance(v, str) and (v.startswith('{') or v.startswith('[')) for v in
                       kwargs['data'].values()):
                    # 对于包含JSON字符串的字段，直接URL编码整个字典
                    kwargs['data'] = '&'.join(
                        f"{k}={quote(str(v), safe='')}"
                        for k, v in kwargs['data'].items()
                    )
                    logger.info("特殊字典")
                else:
                    # 普通字典处理
                    kwargs['data'] = '&'.join(
                        f"{k}={quote(str(v))}"
                        for k, v in kwargs['data'].items()
                    )
                    logger.info("普通字典")
                logger.info(f"URL编码后的请求体: {kwargs['data']}")
            return