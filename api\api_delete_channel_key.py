from api.base_api import BaseApi
from utils.utils import Utils

class APIDeleteChannelKey(BaseApi):
    def __init__(self, session=None):
        super().__init__()
        if session:
            self.session = session
        self.yaml = Utils.read_yaml(r'data/cc_media_test.yaml')

    def delete_channel_key(self, **kwargs):
        req = Utils.get_steps(self.yaml, 'delete_channel_key')
        # 支持参数动态替换
        if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
            for k, v in kwargs.items():
                req['data']['data'][k] = v
        response = self.send_request(**req)
        return response.json()
