config:
    name: testcase description
teststeps:
    - name: "get_channel"
      request:
        method: POST
        url: http://**************:9060/cc-media/webcall
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "309"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-media/pages/media/channel-list.jsp
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
        cookies:
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: 1748920785,1749699583,1750844716,1751248762
            JSESSIONID: 26CF1F5F6B1854204E6AABD31F3FFD07
            JSESSIONID_NODE: B23B6AB03007BB76D22614A6CC307926
            sidebarStatus: "0"
        body:
            data: '{"params":{"channelName":"","channelKey":"6001","channelType":"","channelState":"","toggle":""},"controls":["channel.list","common.getDict(CC_BASE_CHANNEL_STATUS)","common.getDict(CC_BASE_CHANNEL_TYPE)"]}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
    - name: "get_channelSatisfInfo"
      request:
        method: POST
        url: http://**************:9060/cc-media/webcall
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "116"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-media/pages/media/channel-satisfy-edit.html?channelName=web6001&channelKey=6001&channelId=82496000558967451881618
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
        cookies:
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: 1748920785,1749699583,1750844716,1751248762
            JSESSIONID: 26CF1F5F6B1854204E6AABD31F3FFD07
            JSESSIONID_NODE: B23B6AB03007BB76D22614A6CC307926
            sidebarStatus: "0"
        body:
            data: '{"controls":["satisf.channelSatisfInfo"],"params":{"channelNo":"6001"}}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
    - name: "update_channelSatisf"
      request:
        method: POST
        url: http://**************:9060/cc-media/servlet/satisf
        params:
            action: updateChannelSatisf
        headers:
            Accept: application/json, text/javascript, */*; q=0.01
            Accept-Encoding: gzip, deflate
            Accept-Language: zh-CN,zh;q=0.9
            Connection: keep-alive
            Content-Length: "1686"
            Content-Type: application/x-www-form-urlencoded; charset=UTF-8
            Host: **************:9060
            Origin: http://**************:9060
            Referer: http://**************:9060/cc-media/pages/media/channel-satisfy-edit.html?channelName=web6001&channelKey=6001&channelId=82496000558967451881618
            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
            X-Requested-With: XMLHttpRequest
        cookies:
            Hm_lvt_b9e2d180a3f5f12f1c0498f881395280: 1748920785,1749699583,1750844716,1751248762
            JSESSIONID: 26CF1F5F6B1854204E6AABD31F3FFD07
            JSESSIONID_NODE: B23B6AB03007BB76D22614A6CC307926
            sidebarStatus: "0"
        body:
            data: '{"manual":{"satisfyList":[{"NAME":"非常满意","CODE":"1","ITEM_TYPE":"1","detail":[],"detail2":[]},{"NAME":"满意","CODE":"2","ITEM_TYPE":"1","detail":[],"detail2":[]},{"NAME":"一般","CODE":"3","ITEM_TYPE":"2","detail":[],"detail2":[]},{"NAME":"对结果不满意","CODE":"4","ITEM_TYPE":"3","detail":[],"detail2":[]},{"NAME":"对服务不满意","CODE":"5","ITEM_TYPE":"3","detail":[],"detail2":[]}],"startWord":"您好，对刚才客服的解答还满意么？麻烦您为客服回个评价数字哟，您的回复非常重要哦","endWord":"您的评价是我们进步的动力，谢谢。"},"robot":{"satisfyList":[]},"third":{"satisfyList":[]},"video":{"satisfyList":[]},"channelId":"82496000558967451881618","channelNo":"6001","channelName":"web6001"}'
      validate:
        - check: status_code
          assert: equals
          expect: 200
          msg: assert response status code
        - check: headers."Content-Type"
          assert: equals
          expect: application/json;charset=UTF-8
          msg: assert response header Content-Type
        - check: body.msg
          assert: equals
          expect: 操作成功!
          msg: assert response body msg
        - check: body.state
          assert: equals
          expect: 1
          msg: assert response body state
