[2025-07-04 17:20:00] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-04 17:20:00] [base_api.py:39] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/yc-login/login
- 请求头: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'login'}
- 请求体: {'data': '{"j_username": "ODAwMkBla2Y=", "j_password": "2R72jyzx63c8yTH54fCzxQ==", "j_imagecode": "", "callbackUrl": "", "busiId": "007", "loginType": "default", "versionDate": "20220920"}'}
- JSON数据: None
[2025-07-04 17:20:00] [base_api.py:78] [base_api:_handle_form_data] [INFO]- 特殊字典
[2025-07-04 17:20:00] [base_api.py:86] [base_api:_handle_form_data] [INFO]- URL编码后的请求体: data=%7B%22j_username%22%3A%20%22ODAwMkBla2Y%3D%22%2C%20%22j_password%22%3A%20%222R72jyzx63c8yTH54fCzxQ%3D%3D%22%2C%20%22j_imagecode%22%3A%20%22%22%2C%20%22callbackUrl%22%3A%20%22%22%2C%20%22busiId%22%3A%20%22007%22%2C%20%22loginType%22%3A%20%22default%22%2C%20%22versionDate%22%3A%20%2220220920%22%7D
[2025-07-04 17:20:00] [base_api.py:51] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'x-frame-options': 'SAMEORIGIN, SAMEORIGIN', 'X-Content-Type-Options': 'nosniff, nosniff', 'X-XSS-Protection': '1; mode=block, 1; mode=block', 'Content-Security-Policy': 'none', 'Access-Control-Allow-Methods': 'GET,POST', 'Set-Cookie': 'JSESSIONID=61B69C5E78A533E22FBF0907574CB65C; Path=/yc-login; HttpOnly, JSESSIONID_NODE=2875A5196C06D36BF7C1EC377BC80479; Path=/; HttpOnly, mars_token=MAyxGLIO5nBMwEr38SD8P2Z8c6O1H6j+5fC+svxkk3clslBVC/w/70M4/tfQQ8CGidnB0cla00tww37bEBBujMBE3cPa5VHyTnDZgkRFpDw=; Max-Age=43200; Expires=Fri, 04 Jul 2025 21:20:00 GMT; Path=/; Secure; HttpOnly', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Fri, 04 Jul 2025 09:20:00 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: {"msg":"操作成功!","userEntId":"1000","data":[{"BUSI_ORDER_ID":"82546991020289016354594","BUSI_ID":"002","BUSI_NAME":"云电销(基础版)","APP_CONTEXT_PATH":"/yc-portal","BUSI_LOGO_URL":"slt-ydx.png","VALID_DATE":"2025-04-22","INVALID_DATE":"2031-04-22"},{"BUSI_ORDER_ID":"83880897233159997654981","BUSI_ID":"007","BUSI_NAME":"企业呼叫中心","APP_CONTEXT_PATH":"/cc-portal","BUSI_LOGO_URL":"slt-ykf.png","VALID_DATE":"2021-01-29","INVALID_DATE":"2025-01-30"},{"BUSI_ORDER_ID":"82575451131639974023157","BUSI_ID":"015","BUSI_NAME":"智能知识库","APP_CONTEXT_PATH":"/yc-alknowledge","BUSI_LOGO_URL":"slt-ykf.png","VALID_DATE":"2025-03-20","INVALID_DATE":"2031-03-20"},{"BUSI_ORDER_ID":"82552278394919165788429","BUSI_ID":"016","BUSI_NAME":"语音机器人","APP_CONTEXT_PATH":"/mk-robotvue","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-04-16","INVALID_DATE":"2031-04-16"},{"BUSI_ORDER_ID":"82568438201939908172984","BUSI_ID":"018","BUSI_NAME":"智能分析平台","APP_CONTEXT_PATH":"/yc-aianalysis","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-03-28","INVALID_DATE":"2030-03-28"},{"BUSI_ORDER_ID":"82552095494469152320151","BUSI_ID":"201","BUSI_NAME":"智能外呼","APP_CONTEXT_PATH":"/yc-call","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-04-16","INVALID_DATE":"2031-04-16"}],"busiId":"007","userAccount":"8002@ekf","loginTimes":"920","userEntName":"企业呼叫中心","state":1,"busiOrderId":"83880897233159997654981","userId":"83880896957889997330198"}
[2025-07-04 17:20:00] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-04 17:20:00] [base_api.py:39] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/cc-media/servlet/channel
- 请求头: {'Accept': 'application/json, text/javascript, */*; q=0.01', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9', 'Connection': 'keep-alive', 'Content-Length': '164', 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'Host': '**************:9060', 'Origin': 'http://**************:9060', 'Referer': 'http://**************:9060/cc-media/pages/media/channel-key-list.html?channelId=82486620298687314487863&channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&entId=', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'deleteKey'}
- 请求体: {'data': {'keyId': '82485803028659311828429', 'channelId': '82486620298687314487863', 'channelKey': '1111', 'QUEUE_ID': '11'}}
- JSON数据: None
[2025-07-04 17:20:00] [base_api.py:85] [base_api:_handle_form_data] [INFO]- 普通字典
[2025-07-04 17:20:00] [base_api.py:86] [base_api:_handle_form_data] [INFO]- URL编码后的请求体: data=%7B%27keyId%27%3A%20%2782485803028659311828429%27%2C%20%27channelId%27%3A%20%2782486620298687314487863%27%2C%20%27channelKey%27%3A%20%271111%27%2C%20%27QUEUE_ID%27%3A%20%2711%27%7D
[2025-07-04 17:20:00] [base_api.py:51] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'Set-Cookie': 'JSESSIONID=762E81877BDB9770D3DAD622C3557D10; Path=/cc-media; HttpOnly', 'x-frame-options': 'SAMEORIGIN', 'X-Content-Type-Options': 'nosniff', 'X-XSS-Protection': '1; mode=block', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Fri, 04 Jul 2025 09:20:01 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: {"msg":"删除成功！","state":1}
