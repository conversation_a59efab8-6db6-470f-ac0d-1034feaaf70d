{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.7"}, "entries": [{"startedDateTime": "2025-06-20T11:19:39.651+08:00", "time": 73, "request": {"method": "POST", "url": "http://**************:9060/yc-login/login?action=login", "httpVersion": "HTTP/1.1", "cookies": [{"name": "JSESSIONID", "value": "24F599BB958B4D85141E19B76194E0F8"}, {"name": "HMACCOUNT", "value": "028EBC18F491ADCC"}, {"name": "JSESSIONID", "value": "A8940328DBEAD322317476C4BA4A7854"}, {"name": "passport", "value": "Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D"}, {"name": "Hm_lvt_b9e2d180a3f5f12f1c0498f881395280", "value": "**********,**********,**********"}, {"name": "Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280", "value": "**********"}], "headers": [{"name": "Host", "value": "**************:9060"}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "application/json, text/javascript, */*; q=0.01"}, {"name": "Content-Type", "value": "application/x-www-form-urlencoded; charset=UTF-8"}, {"name": "token", "value": "bWFyc0AyMDE5"}, {"name": "Origin", "value": "http://**************:9060"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://**************:9060/cc-portal/login_yq.html?logoutType=1"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "<PERSON><PERSON>", "value": "JSESSIONID=24F599BB958B4D85141E19B76194E0F8; HMACCOUNT=028EBC18F491ADCC; JSESSIONID=A8940328DBEAD322317476C4BA4A7854; passport=Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D; Hm_lvt_b9e2d180a3f5f12f1c0498f881395280=**********,**********,**********; Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280=**********"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "317"}], "queryString": [{"name": "action", "value": "login"}], "postData": {"mimeType": "application/x-www-form-urlencoded; charset=UTF-8", "params": [{"name": "data", "value": "{\"language\":\"zh-CN\",\"j_username\":\"ODAwMkBla2Y=\",\"j_password\":\"2R72jyzx63c8yTH54fCzxQ==\",\"j_imagecode\":\"\",\"callbackUrl\":\"\",\"busiId\":\"007\",\"loginType\":\"default\",\"encryptType\":0,\"versionDate\":\"********\"}"}]}, "headersSize": 1061, "bodySize": 317}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": null, "httpVersion": "HTTP/1.1", "cookies": [{"name": "JSESSIONID", "value": "********************************", "path": "/yc-login", "domain": null, "expires": null, "httpOnly": true, "secure": false, "comment": null, "_maxAge": null}, {"name": "JSESSIONID_NODE", "value": "71BE10D82DE37D43DE766841C7A2B2DA", "path": "/", "domain": null, "expires": null, "httpOnly": true, "secure": false, "comment": null, "_maxAge": null}, {"name": "mars_token", "value": "MAyxGLIO5nBMwEr38SD8P2Z8c6O1H6j+5fC+svxkk3clslBVC/w/70M4/tfQQ8CGidnB0cla00tww37bEBBujMBE3cPa5VHyTnDZgkRFpDw=", "path": "/", "domain": null, "expires": "Fri, 20 Jun 2025 15:19:39 GMT", "httpOnly": true, "secure": true, "comment": null, "_maxAge": "43200"}], "headers": [{"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "Content-Security-Policy", "value": "none"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "X-FRAME-OPTIONS", "value": "SAMEORIGIN"}, {"name": "Access-Control-Allow-Methods", "value": "GET,POST"}, {"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID=********************************; Path=/yc-login; HttpOnly"}, {"name": "Set-<PERSON><PERSON>", "value": "JSESSIONID_NODE=71BE10D82DE37D43DE766841C7A2B2DA; Path=/; HttpOnly"}, {"name": "Set-<PERSON><PERSON>", "value": "mars_token=MAyxGLIO5nBMwEr38SD8P2Z8c6O1H6j+5fC+svxkk3clslBVC/w/70M4/tfQQ8CGidnB0cla00tww37bEBBujMBE3cPa5VHyTnDZgkRFpDw=; Max-Age=43200; Expires=Fri, 20 Jun 2025 15:19:39 GMT; Path=/; Secure; HttpOnly"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Expires", "value": "Thu, 01 Jan 1970 00:00:00 GMT"}, {"name": "vary", "value": "accept-encoding"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "application/json;charset=UTF-8"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Date", "value": "Fri, 20 Jun 2025 03:19:39 GMT"}, {"name": "Keep-Alive", "value": "timeout=20"}, {"name": "Proxy-Connection", "value": "keep-alive"}], "content": {"size": 1480, "compression": 839, "mimeType": "application/json;charset=UTF-8", "text": "eyJtc2ciOiLmk43kvZzmiJDlip8hIiwidXNlckVudElkIjoiMTAwMCIsImRhdGEiOlt7IkJVU0lfT1JERVJfSUQiOiI4MjU0Njk5MTAyMDI4OTAxNjM1NDU5NCIsIkJVU0lfSUQiOiIwMDIiLCJCVVNJX05BTUUiOiLkupHnlLXplIAo5Z+656GA54mIKSIsIkFQUF9DT05URVhUX1BBVEgiOiIveWMtcG9ydGFsIiwiQlVTSV9MT0dPX1VSTCI6InNsdC15ZHgucG5nIiwiVkFMSURfREFURSI6IjIwMjUtMDQtMjIiLCJJTlZBTElEX0RBVEUiOiIyMDMxLTA0LTIyIn0seyJCVVNJX09SREVSX0lEIjoiODM4ODA4OTcyMzMxNTk5OTc2NTQ5ODEiLCJCVVNJX0lEIjoiMDA3IiwiQlVTSV9OQU1FIjoi5LyB5Lia5ZG85Y+r5Lit5b+DIiwiQVBQX0NPTlRFWFRfUEFUSCI6Ii9jYy1wb3J0YWwiLCJCVVNJX0xPR09fVVJMIjoic2x0LXlrZi5wbmciLCJWQUxJRF9EQVRFIjoiMjAyMS0wMS0yOSIsIklOVkFMSURfREFURSI6IjIwMjUtMDEtMzAifSx7IkJVU0lfT1JERVJfSUQiOiI4MjU3NTQ1MTEzMTYzOTk3NDAyMzE1NyIsIkJVU0lfSUQiOiIwMTUiLCJCVVNJX05BTUUiOiLmmbrog73nn6Xor4blupMiLCJBUFBfQ09OVEVYVF9QQVRIIjoiL3ljLWFsa25vd2xlZGdlIiwiQlVTSV9MT0dPX1VSTCI6InNsdC15a2YucG5nIiwiVkFMSURfREFURSI6IjIwMjUtMDMtMjAiLCJJTlZBTElEX0RBVEUiOiIyMDMxLTAzLTIwIn0seyJCVVNJX09SREVSX0lEIjoiODI1NTIyNzgzOTQ5MTkxNjU3ODg0MjkiLCJCVVNJX0lEIjoiMDE2IiwiQlVTSV9OQU1FIjoi6K+t6Z+z5py65Zmo5Lq6IiwiQVBQX0NPTlRFWFRfUEFUSCI6Ii9tay1yb2JvdHZ1ZSIsIkJVU0lfTE9HT19VUkwiOiJzbHQteWgucG5nIiwiVkFMSURfREFURSI6IjIwMjUtMDQtMTYiLCJJTlZBTElEX0RBVEUiOiIyMDMxLTA0LTE2In0seyJCVVNJX09SREVSX0lEIjoiODI1Njg0MzgyMDE5Mzk5MDgxNzI5ODQiLCJCVVNJX0lEIjoiMDE4IiwiQlVTSV9OQU1FIjoi5pm66IO95YiG5p6Q5bmz5Y+wIiwiQVBQX0NPTlRFWFRfUEFUSCI6Ii95Yy1haWFuYWx5c2lzIiwiQlVTSV9MT0dPX1VSTCI6InNsdC15aC5wbmciLCJWQUxJRF9EQVRFIjoiMjAyNS0wMy0yOCIsIklOVkFMSURfREFURSI6IjIwMzAtMDMtMjgifSx7IkJVU0lfT1JERVJfSUQiOiI4MjU1MjA5NTQ5NDQ2OTE1MjMyMDE1MSIsIkJVU0lfSUQiOiIyMDEiLCJCVVNJX05BTUUiOiLmmbrog73lpJblkbwiLCJBUFBfQ09OVEVYVF9QQVRIIjoiL3ljLWNhbGwiLCJCVVNJX0xPR09fVVJMIjoic2x0LXloLnBuZyIsIlZBTElEX0RBVEUiOiIyMDI1LTA0LTE2IiwiSU5WQUxJRF9EQVRFIjoiMjAzMS0wNC0xNiJ9XSwiYnVzaUlkIjoiMDA3IiwidXNlckFjY291bnQiOiI4MDAyQGVrZiIsImxvZ2luVGltZXMiOiI3OTAiLCJ1c2VyRW50TmFtZSI6IuS8geS4muWRvOWPq+S4reW/gyIsInN0YXRlIjoxLCJidXNpT3JkZXJJZCI6IjgzODgwODk3MjMzMTU5OTk3NjU0OTgxIiwidXNlcklkIjoiODM4ODA4OTY5NTc4ODk5OTczMzAxOTgifQ==", "encoding": "base64"}, "redirectURL": null, "headersSize": 950, "bodySize": 641}, "serverIPAddress": "**************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 72, "receive": 1}}, {"startedDateTime": "2025-06-20T11:19:39.746+08:00", "time": 84, "request": {"method": "GET", "url": "http://**************:9060/yc-login/authen?action=success&ctxPath=%2Fcc-portal&busiId=007", "httpVersion": "HTTP/1.1", "cookies": [{"name": "JSESSIONID", "value": "********************************"}, {"name": "HMACCOUNT", "value": "028EBC18F491ADCC"}, {"name": "JSESSIONID", "value": "A8940328DBEAD322317476C4BA4A7854"}, {"name": "passport", "value": "Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D"}, {"name": "Hm_lvt_b9e2d180a3f5f12f1c0498f881395280", "value": "**********,**********,**********"}, {"name": "Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280", "value": "**********"}, {"name": "JSESSIONID_NODE", "value": "71BE10D82DE37D43DE766841C7A2B2DA"}], "headers": [{"name": "Host", "value": "**************:9060"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://**************:9060/cc-portal/login_yq.html?logoutType=1"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "<PERSON><PERSON>", "value": "JSESSIONID=********************************; HMACCOUNT=028EBC18F491ADCC; JSESSIONID=A8940328DBEAD322317476C4BA4A7854; passport=Mjk0N2E1ZTItMTU0My00OTU0LWIwYzAtYjUwMDYzNjkxMjZl%7CMQ%3D%3D%7CMTcxNjc3ODgyOA%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7Cc3VwZXJhZG1pbg%3D%3D%7CMTc1MDA0MjYyNDk0Mw%3D%3D%7CZmI1YTFmNDkyYjg0MjY0ZGNjMWZjOGZjMTk2YzdiODI%3D; Hm_lvt_b9e2d180a3f5f12f1c0498f881395280=**********,**********,**********; Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280=**********; JSESSIONID_NODE=71BE10D82DE37D43DE766841C7A2B2DA"}, {"name": "Connection", "value": "keep-alive"}], "queryString": [{"name": "action", "value": "success"}, {"name": "ctxPath", "value": "/cc-portal"}, {"name": "busiId", "value": "007"}], "headersSize": 1088, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 302, "statusText": null, "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Cache-Control", "value": "private"}, {"name": "x-frame-options", "value": "SAMEORIGIN"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "Content-Security-Policy", "value": "none"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "X-FRAME-OPTIONS", "value": "SAMEORIGIN"}, {"name": "Access-Control-Allow-Methods", "value": "GET,POST"}, {"name": "Location", "value": "/cc-portal"}, {"name": "Content-Length", "value": "0"}, {"name": "Date", "value": "Fri, 20 Jun 2025 03:19:39 GMT"}, {"name": "Keep-Alive", "value": "timeout=20"}, {"name": "Proxy-Connection", "value": "keep-alive"}], "content": {"size": 0, "mimeType": null, "text": "", "encoding": "base64"}, "redirectURL": "/cc-portal", "headersSize": 427, "bodySize": 0}, "serverIPAddress": "**************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 0, "wait": 84, "receive": 0}}]}}