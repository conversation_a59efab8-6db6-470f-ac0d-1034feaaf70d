import pytest
from api.cc_media import CCMEDIA
from api.api_add_web_autoConfig import APIAddWebAutoconfig
from api.api_get_channelList import APIGetChannellist
from api.api_delete_web_channel import APIDeleteWebChannel
from api.api_channel_set_robot import APIChannelSetRobot
from api.api_updates import APIUpdates
from api.api_batch_delete_by_ids import APIBatchDeleteByIds
from api.api_get_channel_by_input import APIGetChannelByInput
from api.api_add_channel_key import APIAddChannelKey
from api.api_update_channel_key import APIUpdateChannelKey
from api.api_get_channel_keyList import APIGetChannelKeylist
from api.api_delete_channel_key import APIDeleteChannelKey
from api.api_get_customer_url import APIGetCustomerUrl
from api.api_update_style_config import APIUpdateStyleConfig
from api.api_update_channel_satisfy import APIUpdateChannelSatisfy



@pytest.fixture(scope="class")
def api_client(api_client):
    """创建渠道管理客户端"""
    return CCMEDIA(session=api_client.session)


class TestCCMedia:
    """渠道管理测试类"""

    def test_add_web_channel(self, api_client):
        # 调用新增网页渠道接口
        res = api_client.add_web_channel(
            CHANNEL_NAME="web6001",
            CHANNEL_KEY="10003",
            BEGIN_WORK_TIME1="00:00",
            END_WORK_TIME1="23:59",
            WEB_URL="http://172.16.110.241:9060"
        )

        # 基本断言：验证响应成功
        assert res.get("msg") == "添加成功！", f"新增渠道失败: {res.get('msg')}"

def test_add_web_autoConfig(api_client):
    res = api_client.add_web_autoConfig()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_get_channelList(api_client):
    res = api_client.get_channelList()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_delete_web_channel(api_client):
    res = api_client.delete_web_channel()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_channel_set_robot(api_client):
    res = api_client.channel_set_robot()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_updates(api_client):
    res = api_client.updates()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_batch_delete_by_ids(api_client):
    res = api_client.batch_delete_by_ids()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_get_channel_by_input(api_client):
    res = api_client.get_channel_by_input()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_add_channel_key(api_client):
    res = api_client.add_channel_key()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_update_channel_key(api_client):
    res = api_client.update_channel_key()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_get_channel_keyList(api_client):
    res = api_client.get_channel_keyList()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_delete_channel_key(api_client):
    res = api_client.delete_channel_key()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_get_customer_url(api_client):
    res = api_client.get_customer_url()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_update_style_config(api_client):
    res = api_client.update_style_config()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)

def test_update_channel_satisfy(api_client):
    res = api_client.update_channel_satisfy()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)