[2025-07-07 13:33:56] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-07 13:33:56] [base_api.py:39] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/yc-login/login
- 请求头: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'login'}
- 请求体: {'data': '{"j_username": "ODAwMkBla2Y=", "j_password": "2R72jyzx63c8yTH54fCzxQ==", "j_imagecode": "", "callbackUrl": "", "busiId": "007", "loginType": "default", "versionDate": "20220920"}'}
- JSON数据: None
[2025-07-07 13:33:56] [base_api.py:78] [base_api:_handle_form_data] [INFO]- 特殊字典
[2025-07-07 13:33:56] [base_api.py:86] [base_api:_handle_form_data] [INFO]- URL编码后的请求体: data=%7B%22j_username%22%3A%20%22ODAwMkBla2Y%3D%22%2C%20%22j_password%22%3A%20%222R72jyzx63c8yTH54fCzxQ%3D%3D%22%2C%20%22j_imagecode%22%3A%20%22%22%2C%20%22callbackUrl%22%3A%20%22%22%2C%20%22busiId%22%3A%20%22007%22%2C%20%22loginType%22%3A%20%22default%22%2C%20%22versionDate%22%3A%20%2220220920%22%7D
[2025-07-07 13:33:56] [base_api.py:51] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'x-frame-options': 'SAMEORIGIN, SAMEORIGIN', 'X-Content-Type-Options': 'nosniff, nosniff', 'X-XSS-Protection': '1; mode=block, 1; mode=block', 'Content-Security-Policy': 'none', 'Access-Control-Allow-Methods': 'GET,POST', 'Set-Cookie': 'JSESSIONID=AA84365169371BD9CB13D986011C2B6F; Path=/yc-login; HttpOnly, mars_token=; Max-Age=0; Expires=Thu, 01 Jan 1970 00:00:10 GMT; Path=/; Secure', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 07 Jul 2025 05:33:56 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: {"msg":"操作过于频繁，请稍后再试","state":0}
[2025-07-07 13:33:56] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
