# generate_api.py 使用说明

## 功能概述

优化后的 `generate_api.py` 支持以下功能：

1. **生成新的 API 和测试文件**（原有功能）
2. **在现有文件中添加方法和测试用例**（新功能）
3. **避免重复生成代码**（新功能）
4. **灵活控制生成内容**（新功能）

## 命令行参数

### 基本参数
- `yaml_path`: YAML 文件路径（必需）
- `--name`: 接口步骤名称（必需）

### 控制参数
- `--no-api`: 不生成新的 API 文件
- `--no-test`: 不生成新的测试文件
- `--api-file`: 指定现有的 API 文件路径，在其中添加方法
- `--test-file`: 指定现有的测试文件路径，在其中添加测试用例

## 使用示例

### 1. 生成新的 API 和测试文件（原有功能）
```bash
python generate_api.py data/cc_media_test.yaml --name get_channelList
```

### 2. 只生成 API 文件，不生成测试文件
```bash
python generate_api.py data/cc_media_test.yaml --name get_channelList --no-test
```

### 3. 只生成测试文件，不生成 API 文件
```bash
python generate_api.py data/cc_media_test.yaml --name get_channelList --no-api
```

### 4. 在现有 API 文件中添加新方法
```bash
python generate_api.py data/cc_media_test.yaml --name get_channelList --api-file api/cc_media.py
```

### 5. 在现有测试文件中添加新测试用例
```bash
python generate_api.py data/cc_media_test.yaml --name get_channelList --test-file testcases/test_cc_media.py
```

### 6. 同时在现有的 API 和测试文件中添加代码
```bash
python generate_api.py data/cc_media_test.yaml --name get_channelList --api-file api/cc_media.py --test-file testcases/test_cc_media.py
```

### 7. 只在现有文件中添加代码，不生成新文件
```bash
python generate_api.py data/cc_media_test.yaml --name get_channelList --no-api --no-test --api-file api/cc_media.py --test-file testcases/test_cc_media.py
```

## 智能重复检测

### API 文件
- 检查方法是否已存在，避免重复添加
- 自动在类的末尾添加新方法

### 测试文件
- 检查测试用例是否已存在，避免重复添加
- 检查 fixture 是否已存在，避免重复添加
- 自动添加必要的 import 语句
- 确保生成的 fixture 包含 `session=api_client.session` 参数

## 生成的代码特点

### API 方法模板
```python
def {name}(self, **kwargs):
    req = Utils.get_steps(self.yaml, '{name}')
    # 支持参数动态替换
    if 'data' in req and 'data' in req['data'] and isinstance(req['data']['data'], dict):
        for k, v in kwargs.items():
            req['data']['data'][k] = v
    response = self.send_request(**req)
    return response.json()
```

### 测试用例模板
```python
def test_{name}(api_client):
    res = api_client.{name}()
    # 断言可根据yaml validate自动生成
    assert isinstance(res, dict)
```

### Fixture 模板（修复了 session 问题）
```python
@pytest.fixture(scope='class')
def api_client(api_client):
    return API{name_camel}(session=api_client.session)
```

## 注意事项

1. **文件路径**: 使用相对路径时，确保在项目根目录下运行命令
2. **重复检测**: 工具会自动检测重复的方法、测试用例和 fixture，避免重复生成
3. **Import 语句**: 在现有测试文件中添加测试用例时，会自动添加必要的 import 语句
4. **Session 参数**: 生成的 fixture 会正确包含 `session=api_client.session` 参数，解决了原有的问题
