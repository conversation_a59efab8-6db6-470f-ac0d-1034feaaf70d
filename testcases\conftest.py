import os
import pytest
from api.mars_api import MARSAPI


@pytest.fixture(scope="session")
def api_client():
    """
    创建已登录的 API 客户端
    从环境变量获取登录凭证，如果环境变量未设置则使用默认值
    """
    # 从环境变量获取登录凭证，如果未设置则使用默认值
    username = os.getenv("CC_USERNAME", "8002@ekf")
    password = os.getenv("CC_PASSWORD", "2R72jyzx63c8yTH54fCzxQ==")

    client = MARSAPI()
    client.login(username=username, password=password)
    return client