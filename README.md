# 🚀 API 自动化测试框架

基于 Python + Pytest + HttpRunner 的 API 自动化测试框架，支持快速生成 API 接口和测试用例。

## 📋 目录

- [项目特性](#-项目特性)
- [项目结构](#-项目结构)
- [环境要求](#-环境要求)
- [快速开始](#-快速开始)
- [API 生成工具](#-api-生成工具)
- [测试规范](#-测试规范)
- [HttpRunner 集成](#-httprunner-集成)
- [环境配置](#-环境配置)
- [贡献指南](#-贡献指南)

## ✨ 项目特性

- 🔧 **智能代码生成**：基于 YAML 配置自动生成 API 接口和测试用例
- 🎯 **避免重复代码**：智能检测已存在的方法和测试用例，避免重复生成
- 🔄 **灵活的生成模式**：支持生成新文件或在现有文件中添加代码
- 📊 **完整的测试框架**：集成 Pytest + HttpRunner，支持多种测试报告格式
- 🌐 **多环境支持**：通过配置文件轻松切换测试环境
- 📝 **标准化规范**：遵循 Pytest 测试规范和最佳实践

## 📁 项目结构

```
testProject2025/
├── api/                    # API 接口封装
│   ├── base_api.py        # 基础 API 类
│   ├── mars_api.py        # Mars API 封装
│   └── cc_media.py        # 渠道管理 API
├── data/                   # 测试数据和配置
│   ├── env.yml            # 环境配置
│   ├── login.yml          # 登录配置
│   └── cc_media_test.yaml # 接口测试数据
├── testcases/             # 测试用例
│   ├── conftest.py        # Pytest 配置
│   └── test_*.py          # 测试用例文件
├── utils/                 # 工具类
│   ├── logger.py          # 日志工具
│   └── utils.py           # 通用工具
├── har/                   # HAR 文件存储
├── logs/                  # 日志文件
├── generate_api.py        # API 生成工具
└── requirements.txt       # 项目依赖
```

## 🔧 环境要求

- Python 3.8+
- pip 包管理器

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd testProject2025
```

### 2. 安装依赖

```bash
# 升级 pip
python -m pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

### 3. 运行测试

```bash
# 运行所有测试
pytest

# 运行指定测试文件
pytest testcases/test_cc_media.py

# 生成 HTML 测试报告
pytest --html=reports/report.html
```

## 🛠 API 生成工具

### 基本用法

```bash
# 查看帮助
python generate_api.py --help

# 生成新的 API 文件和测试用例
python generate_api.py data/cc_media_test.yaml --name get_channelList
```

### 高级用法

```bash
# 在现有 API 文件中添加方法
python generate_api.py data/cc_media_test.yaml --name get_channelList --api-file api/cc_media.py

# 在现有测试文件中添加测试用例
python generate_api.py data/cc_media_test.yaml --name get_channelList --test-file testcases/test_cc_media.py

# 同时在现有文件中添加 API 方法和测试用例
python generate_api.py data/cc_media_test.yaml --name get_channelList \
  --api-file api/cc_media.py --test-file testcases/test_cc_media.py

# 只生成 API 文件，不生成测试文件
python generate_api.py data/cc_media_test.yaml --name get_channelList --no-test

# 只生成测试文件，不生成 API 文件
python generate_api.py data/cc_media_test.yaml --name get_channelList --no-api
```

### 智能特性

- ✅ **重复检测**：自动检测已存在的方法和测试用例，避免重复生成
- ✅ **Import 管理**：自动添加必要的 import 语句
- ✅ **Session 处理**：生成的 fixture 正确包含 `session=api_client.session` 参数
- ✅ **代码格式化**：生成的代码符合 Python 编码规范

详细使用说明请参考：[generate_api_usage.md](generate_api_usage.md)

## 📋 测试规范

### Pytest 命名规范

- **测试文件**：以 `test_` 开头或以 `_test` 结尾
- **测试类**：以 `Test` 开头
- **测试方法**：以 `test_` 开头

### 示例

```python
# testcases/test_example.py
import pytest

class TestExample:
    def test_api_call(self, api_client):
        response = api_client.get_data()
        assert response.status_code == 200
```

## 🔗 HttpRunner 集成

本项目集成了 HttpRunner 框架，支持多种文件格式转换：

```bash
# 查看版本
hrp -v

# HAR 文件转换
hrp convert --from-har example.har --to-yaml    # 转换为 YAML
hrp convert --from-har example.har --to-json    # 转换为 JSON
hrp convert --from-har example.har --to-pytest  # 转换为 Pytest
hrp convert --from-har example.har --to-gotest  # 转换为 Go 测试
```

**官方文档**：https://github.com/HttpRunner/HttpRunner

## 🌍 环境配置

通过 `data/env.yml` 文件配置不同的测试环境：

```yaml
# data/env.yml
dev:
  host: "http://dev.example.com"
  port: 8080

test:
  host: "http://test.example.com"
  port: 8080

prod:
  host: "http://prod.example.com"
  port: 443
```

## 📦 依赖管理

### 更新依赖文件

```bash
pip freeze > requirements.txt
```

### 主要依赖

- `pytest==8.4.0` - 测试框架
- `httprunner==4.3.5` - HTTP 测试框架
- `requests==2.32.4` - HTTP 请求库
- `PyYAML==6.0.2` - YAML 解析
- `openpyxl==3.1.5` - Excel 文件处理
- `pystache==0.6.8` - 模板引擎

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request


**注意**：使用前请确保已正确配置测试环境和相关依赖。如有问题，请查看日志文件或提交 Issue。