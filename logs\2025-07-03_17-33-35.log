[2025-07-03 17:33:36] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-03 17:33:36] [base_api.py:39] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/yc-login/login
- 请求头: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'login'}
- 请求体: {'data': '{"j_username": "ODAwMkBla2Y=", "j_password": "2R72jyzx63c8yTH54fCzxQ==", "j_imagecode": "", "callbackUrl": "", "busiId": "007", "loginType": "default", "versionDate": "20220920"}'}
- JSON数据: None
[2025-07-03 17:33:36] [base_api.py:83] [base_api:_handle_form_data] [INFO]- URL编码后的请求体: data=%7B%22j_username%22%3A%20%22ODAwMkBla2Y%3D%22%2C%20%22j_password%22%3A%20%222R72jyzx63c8yTH54fCzxQ%3D%3D%22%2C%20%22j_imagecode%22%3A%20%22%22%2C%20%22callbackUrl%22%3A%20%22%22%2C%20%22busiId%22%3A%20%22007%22%2C%20%22loginType%22%3A%20%22default%22%2C%20%22versionDate%22%3A%20%2220220920%22%7D
[2025-07-03 17:33:36] [base_api.py:51] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 200
- 响应头: {'x-frame-options': 'SAMEORIGIN, SAMEORIGIN', 'X-Content-Type-Options': 'nosniff, nosniff', 'X-XSS-Protection': '1; mode=block, 1; mode=block', 'Content-Security-Policy': 'none', 'Access-Control-Allow-Methods': 'GET,POST', 'Set-Cookie': 'JSESSIONID=ADFC8BB5B88A9A3B1ACC02DDF78D841C; Path=/yc-login; HttpOnly, JSESSIONID_NODE=71D4984A3BBC463F6ACFF12E88FC0F3D; Path=/; HttpOnly, mars_token=MAyxGLIO5nBMwEr38SD8P2Z8c6O1H6j+5fC+svxkk3clslBVC/w/70M4/tfQQ8CGidnB0cla00tww37bEBBujMBE3cPa5VHyTnDZgkRFpDw=; Max-Age=43200; Expires=Thu, 03 Jul 2025 21:33:37 GMT; Path=/; Secure; HttpOnly', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'vary': 'accept-encoding', 'Content-Encoding': 'gzip', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Thu, 03 Jul 2025 09:33:37 GMT', 'Keep-Alive': 'timeout=20', 'Connection': 'keep-alive'}
- 响应体: {"msg":"操作成功!","userEntId":"1000","data":[{"BUSI_ORDER_ID":"82546991020289016354594","BUSI_ID":"002","BUSI_NAME":"云电销(基础版)","APP_CONTEXT_PATH":"/yc-portal","BUSI_LOGO_URL":"slt-ydx.png","VALID_DATE":"2025-04-22","INVALID_DATE":"2031-04-22"},{"BUSI_ORDER_ID":"83880897233159997654981","BUSI_ID":"007","BUSI_NAME":"企业呼叫中心","APP_CONTEXT_PATH":"/cc-portal","BUSI_LOGO_URL":"slt-ykf.png","VALID_DATE":"2021-01-29","INVALID_DATE":"2025-01-30"},{"BUSI_ORDER_ID":"82575451131639974023157","BUSI_ID":"015","BUSI_NAME":"智能知识库","APP_CONTEXT_PATH":"/yc-alknowledge","BUSI_LOGO_URL":"slt-ykf.png","VALID_DATE":"2025-03-20","INVALID_DATE":"2031-03-20"},{"BUSI_ORDER_ID":"82552278394919165788429","BUSI_ID":"016","BUSI_NAME":"语音机器人","APP_CONTEXT_PATH":"/mk-robotvue","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-04-16","INVALID_DATE":"2031-04-16"},{"BUSI_ORDER_ID":"82568438201939908172984","BUSI_ID":"018","BUSI_NAME":"智能分析平台","APP_CONTEXT_PATH":"/yc-aianalysis","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-03-28","INVALID_DATE":"2030-03-28"},{"BUSI_ORDER_ID":"82552095494469152320151","BUSI_ID":"201","BUSI_NAME":"智能外呼","APP_CONTEXT_PATH":"/yc-call","BUSI_LOGO_URL":"slt-yh.png","VALID_DATE":"2025-04-16","INVALID_DATE":"2031-04-16"}],"busiId":"007","userAccount":"8002@ekf","loginTimes":"899","userEntName":"企业呼叫中心","state":1,"busiOrderId":"83880897233159997654981","userId":"83880896957889997330198"}
[2025-07-03 17:33:36] [utils.py:294] [utils:get_steps] [INFO]- 当前环境: test, base_url: http://**************:9060
[2025-07-03 17:33:36] [base_api.py:39] [base_api:_log_request] [INFO]- 
请求信息:
- 方法: POST
- URL: http://**************:9060/cc-media/servlet/satisf
- 请求头: {'Accept': 'application/json, text/javascript, */*; q=0.01', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9', 'Connection': 'keep-alive', 'Content-Length': '3014', 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 'Host': '**************:9060', 'Origin': 'http://**************:9060', 'Referer': 'http://**************:9060/cc-media/pages/media/channel-satisfy-edit.html?channelName=%E6%B5%8B%E8%AF%95%E7%BD%91%E9%A1%B5&channelKey=1111&channelId=82486620298687314487863', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'X-Requested-With': 'XMLHttpRequest', 'token': 'bWFyc0AyMDE5'}
- 查询参数: {'action': 'updateChannelSatisf'}
- 请求体: %7B%22manual%22%3A%7B%22satisfyList%22%3A%5B%7B%22NAME%22%3A%22%E9%9D%9E%E5%B8%B8%E6%BB%A1%E6%84%8F%22%2C%22CODE%22%3A%221%22%2C%22ITEM_TYPE%22%3A%221%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%80%81%E5%BA%A6%22%2C%22CODE%22%3A%221%22%2C%22SORT_NUM%22%3A%221%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%2C%7B%22NAME%22%3A%22%E6%BB%A1%E6%84%8F%22%2C%22CODE%22%3A%222%22%2C%22ITEM_TYPE%22%3A%221%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E4%B8%93%E4%B8%9A%E6%B0%B4%E5%B9%B3%22%2C%22CODE%22%3A%222%22%2C%22SORT_NUM%22%3A%222%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%2C%7B%22NAME%22%3A%22%E4%B8%80%E8%88%AC%22%2C%22CODE%22%3A%223%22%2C%22ITEM_TYPE%22%3A%222%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E6%B2%9F%E9%80%9A%E8%A1%A8%E8%BE%BE%22%2C%22CODE%22%3A%223%22%2C%22SORT_NUM%22%3A%223%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%2C%7B%22NAME%22%3A%22%E5%AF%B9%E7%BB%93%E6%9E%9C%E4%B8%8D%E6%BB%A1%E6%84%8F%22%2C%22CODE%22%3A%224%22%2C%22ITEM_TYPE%22%3A%223%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E5%93%8D%E5%BA%94%E9%80%9F%E5%BA%A6%22%2C%22CODE%22%3A%224%22%2C%22SORT_NUM%22%3A%224%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%2C%7B%22NAME%22%3A%22%E5%AF%B9%E6%9C%8D%E5%8A%A1%E4%B8%8D%E6%BB%A1%E6%84%8F%22%2C%22CODE%22%3A%225%22%2C%22ITEM_TYPE%22%3A%223%22%2C%22detail%22%3A%5B%7B%22NAME%22%3A%22%E5%93%8D%E5%BA%94%E9%80%9F%E5%BA%A6%22%2C%22CODE%22%3A%224%22%2C%22SORT_NUM%22%3A%224%22%2C%22ITEM2_TYPE%22%3A%221%22%7D%5D%2C%22detail2%22%3A%5B%7B%22NAME%22%3A%22%E6%9C%8D%E5%8A%A1%E6%95%88%E7%8E%87%22%2C%22CODE%22%3A%22123we%22%2C%22SORT_NUM%22%3A%221%22%7D%5D%7D%5D%2C%22startWord%22%3A%22%E6%82%A8%E5%A5%BD%EF%BC%8C%E5%AF%B9%E5%88%9A%E6%89%8D%E5%AE%A2%E6%9C%8D%E7%9A%84%E8%A7%A3%E7%AD%94%E8%BF%98%E6%BB%A1%E6%84%8F%E4%B9%88%EF%BC%9F%E9%BA%BB%E7%83%A6%E6%82%A8%E4%B8%BA%E5%AE%A2%E6%9C%8D%E5%9B%9E%E4%B8%AA%E8%AF%84%E4%BB%B7%E6%95%B0%E5%AD%97%E5%93%9F%EF%BC%8C%E6%82%A8%E7%9A%84%E5%9B%9E%E5%A4%8D%E9%9D%9E%E5%B8%B8%E9%87%8D%E8%A6%81%E5%93%A6%22%2C%22endWord%22%3A%22%E6%82%A8%E7%9A%84%E8%AF%84%E4%BB%B7%E6%98%AF%E6%88%91%E4%BB%AC%E8%BF%9B%E6%AD%A5%E7%9A%84%E5%8A%A8%E5%8A%9B%EF%BC%8C%E8%B0%A2%E8%B0%A2%E3%80%82%22%7D%2C%22robot%22%3A%7B%22satisfyList%22%3A%5B%5D%7D%2C%22third%22%3A%7B%22satisfyList%22%3A%5B%5D%7D%2C%22video%22%3A%7B%22satisfyList%22%3A%5B%5D%7D%2C%22channelId%22%3A%2282486620298687314487863%22%2C%22channelNo%22%3A%221111%22%2C%22channelName%22%3A%22%25E6%25B5%258B%25E8%25AF%2595%25E7%25BD%2591%25E9%25A1%25B5%22%7D
- JSON数据: None
[2025-07-03 17:33:36] [base_api.py:51] [base_api:_log_response] [INFO]- 
响应信息:
- 状态码: 500
- 响应头: {'Set-Cookie': 'JSESSIONID=43FA26F5DC0331FA85510ADB9CF4D5AE; Path=/cc-media; HttpOnly', 'x-frame-options': 'SAMEORIGIN', 'X-Content-Type-Options': 'nosniff', 'X-XSS-Protection': '1; mode=block', 'Content-Type': 'text/html;charset=utf-8', 'Content-Length': '1058', 'Date': 'Thu, 03 Jul 2025 09:33:37 GMT', 'Connection': 'close'}
- 响应体: <!DOCTYPE html>

<html>

	<head>

		<title>系统发生错误 -500 </title>

		<link rel="icon" type="image/x-icon" href="favicon.ico">

		<style type="text/css">

			.body {

			  color: #666;

			  text-align: center;

			  font-family: Helvetica, 'microsoft yahei', Arial, sans-serif;

			  margin:0;

			  width: 800px;

			  margin: auto;

			  font-size: 14px;

			}

			h1 {

			  font-size: 56px;

			  font-weight: normal;

			  color: #456;

			}

			h2 { font-size: 24px; color: #666; line-height: 1.5em; }

			

			h3 {

			  color: #456;

			  font-size: 20px;

			  font-weight: normal;

			  line-height: 28px;

			}

			

			hr {

			  margin: 18px 0;

			  border: 0;

			  border-top: 1px solid #EEE;

			  border-bottom: 1px solid white;

			}

			

			a{

			    color: #17bc9b;

			    text-decoration: none;

			}

		</style>

	</head>

<body class="body">

 	<h1>500</h1>

  <h3>系统发生错误.</h3>

  <hr/>

  <p>如果问题持续存在，请与我们联系。 <br><br><a href="/cc-media">返回首页</a></p>

	</body>

</html>
